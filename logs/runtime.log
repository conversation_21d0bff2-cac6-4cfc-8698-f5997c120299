[09-10 10:28:14.849] [info] =====算法库初始化开始=====
[09-10 10:28:14.849] [info] Initializing unified resource manager...
[09-10 10:28:14.849] [info] Initializing GPU resource manager...
[09-10 10:28:14.942] [info] GPU resource manager initialized successfully
[09-10 10:28:14.943] [info] Initializing memory pool manager...
[09-10 10:28:14.943] [info] CUDA memory pool created with max size: 512MB
[09-10 10:28:14.943] [info] Host memory pool created with max size: 1024MB
[09-10 10:28:14.943] [info] Memory pool manager initialized successfully
[09-10 10:28:14.943] [info] Initializing FFT GPU Optimizer with provided GPU manager: 1024x2048
[09-10 10:28:14.943] [info] Initializing FFT resource pools for 5 segment lengths
[09-10 10:28:14.945] [info] Resource pool initialized for length 512: 10 buffers, 10 plans
[09-10 10:28:14.945] [info] Resource pool initialized for length 256: 10 buffers, 10 plans
[09-10 10:28:14.946] [info] Resource pool initialized for length 128: 10 buffers, 10 plans
[09-10 10:28:14.946] [info] Resource pool initialized for length 64: 10 buffers, 10 plans
[09-10 10:28:14.946] [info] All FFT resource pools initialized successfully
[09-10 10:28:14.946] [info] FFT GPU Optimizer initialized successfully with provided GPU manager
[09-10 10:28:14.946] [info] Unified resource manager initialized successfully
[09-10 10:28:14.946] [info] Loading TensorRT engine from: data/0908-fp16.trt
[09-10 10:28:14.960] [info] TensorRT engine loaded successfully: data/0908-fp16.trt
[09-10 10:28:14.960] [info] Engine info: 2 bindings
[09-10 10:28:14.960] [info]   Binding 0: input [1x1x1024x1024x2]
[09-10 10:28:14.960] [info]   Binding 1: output [1x1x512x1024]
[09-10 10:28:14.960] [info] 模型输入维度: [1x1024x1024x2]
[09-10 10:28:14.960] [info] GPU buffers allocated: input=8 MB, output=2 MB
[09-10 10:28:14.964] [info] 加载俯仰角查表数据: data/hecha_table.csv
[09-10 10:28:14.964] [info] - 显存 - 算法初始化完成: 使用 824.6 MB
[09-10 10:28:14.964] [info] - 内存 - 算法初始化完成: 使用 258.7 MB
[09-10 10:28:14.964] [info] =====算法库初始化完成=====
[09-10 10:28:15.055] [info] =====开始目标检测=====
[09-10 10:28:18.046] [info] 提取到 1024 个有效帧头
[09-10 10:28:19.508] [warning] 无效帧:302 模型没有检测到目标，跳过处理
[09-10 10:28:25.216] [info] =====开始目标跟踪======
[09-10 10:28:25.216] [info] 检测数据: num_detections=0, num_segments=0
[09-10 10:28:25.216] [info] FFT和俯仰角计算完成, 得到 0 个结果：
[09-10 10:28:25.216] [info] 跟踪结果数量: 0
[09-10 10:28:25.216] [info] 已释放内部检测数据内存
[09-10 10:28:25.216] [info] - 显存 - 目标跟踪完成: 使用 827.4 MB
[09-10 10:28:25.216] [info] - 内存 - 目标跟踪完成: 使用 597.4 MB
[09-10 10:28:25.234] [info] =====开始目标检测=====
[09-10 10:28:30.394] [info] 提取到 1024 个有效帧头
[09-10 10:28:30.405] [warning] 无效帧:303 模型没有检测到目标，跳过处理
[09-10 10:28:30.405] [info] =====开始目标跟踪======
[09-10 10:28:30.405] [info] 检测数据: num_detections=0, num_segments=0
[09-10 10:28:30.405] [info] FFT和俯仰角计算完成, 得到 0 个结果：
[09-10 10:28:30.405] [info] 跟踪结果数量: 0
[09-10 10:28:30.405] [info] 已释放内部检测数据内存
[09-10 10:28:30.405] [info] - 显存 - 目标跟踪完成: 使用 835.6 MB
[09-10 10:28:30.405] [info] - 内存 - 目标跟踪完成: 使用 491.6 MB
[09-10 10:28:30.494] [info] =====开始目标检测=====
[09-10 10:28:36.144] [info] 提取到 1024 个有效帧头
[09-10 10:28:37.419] [info] 检测到 2 个目标中心点
[09-10 10:28:37.419] [info] 开始提取列数据段，共 2 个中心点
[09-10 10:28:37.420] [info] 检测点[0]: 帧号=304, 列=1872, 行=450, 段长度=512
[09-10 10:28:37.420] [info] 检测点[1]: 帧号=304, 列=1872, 行=708, 段长度=256
[09-10 10:28:37.420] [info] 目标检测耗时(ms): 预处理:4 推理:2 后处理:2 提取:0 总:8 (FPS:125.00)
[09-10 10:28:37.420] [info] =====开始目标跟踪======
[09-10 10:28:37.420] [info] 检测数据: num_detections=2, num_segments=2
[09-10 10:28:37.420] [info] FFT和俯仰角计算完成, 得到 2 个结果：
[09-10 10:28:37.420] [info] Frame: 304, Velo: -11.35, Range: 1211.00, Amaz:  22.50, Elev:   5.00, Altitude: 105.55, X_cor: 1872, Y_cor: 450
[09-10 10:28:37.420] [info] Frame: 304, Velo: -10.99, Range: 1211.00, Amaz:  22.06, Elev:   6.00, Altitude: 126.58, X_cor: 1872, Y_cor: 708
[09-10 10:28:37.422] [info] 跟踪结果数量: 0
[09-10 10:28:37.422] [info] 已释放内部检测数据内存
[09-10 10:28:37.422] [info] - 显存 - 目标跟踪完成: 使用 837.9 MB
[09-10 10:28:37.422] [info] - 内存 - 目标跟踪完成: 使用 492.2 MB
[09-10 10:28:37.509] [info] =====开始目标检测=====
[09-10 10:28:55.357] [info] 提取到 1024 个有效帧头
[09-10 10:28:55.368] [info] 检测到 2 个目标中心点
[09-10 10:28:55.368] [info] 开始提取列数据段，共 2 个中心点
[09-10 10:28:55.368] [info] 检测点[0]: 帧号=305, 列=1872, 行=449, 段长度=512
[09-10 10:28:55.368] [info] 检测点[1]: 帧号=305, 列=1872, 行=707, 段长度=256
[09-10 10:28:55.368] [info] 目标检测耗时(ms): 预处理:5 推理:2 后处理:2 提取:0 总:9 (FPS:111.11)
[09-10 10:28:55.368] [info] =====开始目标跟踪======
[09-10 10:28:55.368] [info] 检测数据: num_detections=2, num_segments=2
[09-10 10:28:55.368] [info] FFT和俯仰角计算完成, 得到 2 个结果：
[09-10 10:28:55.368] [info] Frame: 305, Velo: -11.54, Range: 1211.00, Amaz:  25.60, Elev:   4.70, Altitude:  99.23, X_cor: 1872, Y_cor: 449
[09-10 10:28:55.368] [info] Frame: 305, Velo: -11.17, Range: 1211.00, Amaz:  25.17, Elev:   5.60, Altitude: 118.17, X_cor: 1872, Y_cor: 707
[09-10 10:28:55.371] [info] 跟踪结果数量: 2
[09-10 10:28:55.371] [info] Track[ 0] ID:  0 | Pos: (1090.81,  512.54,  118.16) m | Vel: (-10.06,  -4.72,  -1.09) m/s | R: 1211.00 m | Vr: -11.17 m/s | Az:  25.17° | El:   5.60°
[09-10 10:28:55.371] [info] Track[ 1] ID:  1 | Pos: (1088.47,  521.43,   99.25) m | Vel: (-10.37,  -4.96,  -0.95) m/s | R: 1211.00 m | Vr: -11.53 m/s | Az:  25.60° | El:   4.70°
[09-10 10:28:55.371] [info] 跟踪完成，输出 2 个轨迹
[09-10 10:28:55.371] [info] 已释放内部检测数据内存
[09-10 10:28:55.371] [info] - 显存 - 目标跟踪完成: 使用 821.1 MB
[09-10 10:28:55.371] [info] - 内存 - 目标跟踪完成: 使用 493.7 MB
[09-10 10:28:55.455] [info] =====开始目标检测=====
[09-10 10:28:59.022] [info] 提取到 1024 个有效帧头
[09-10 10:28:59.988] [warning] 无效帧:306 模型没有检测到目标，跳过处理
[09-10 10:28:59.988] [info] =====开始目标跟踪======
[09-10 10:28:59.988] [info] 检测数据: num_detections=0, num_segments=0
[09-10 10:28:59.988] [info] FFT和俯仰角计算完成, 得到 0 个结果：
[09-10 10:28:59.988] [info] 跟踪结果数量: 0
[09-10 10:28:59.988] [info] Track[ 0] ID:  0 | Pos: (1090.30,  512.30,  118.11) m | Vel: (-10.06,  -4.72,  -1.09) m/s | R: 1210.44 m | Vr: -11.17 m/s | Az:  25.17° | El:   5.60°
[09-10 10:28:59.988] [info] Track[ 1] ID:  1 | Pos: (1087.96,  521.18,   99.21) m | Vel: (-10.37,  -4.96,  -0.95) m/s | R: 1210.42 m | Vr: -11.53 m/s | Az:  25.60° | El:   4.70°
[09-10 10:28:59.988] [info] 跟踪完成，输出 2 个轨迹
[09-10 10:28:59.988] [info] 已释放内部检测数据内存
[09-10 10:28:59.988] [info] - 显存 - 目标跟踪完成: 使用 830.7 MB
[09-10 10:28:59.988] [info] - 内存 - 目标跟踪完成: 使用 492.5 MB
[09-10 10:29:00.073] [info] =====开始目标检测=====
[09-10 10:29:02.071] [info] 提取到 1024 个有效帧头
[09-10 10:29:04.614] [warning] 无效帧:307 模型没有检测到目标，跳过处理
[09-10 10:29:04.614] [info] =====开始目标跟踪======
[09-10 10:29:04.614] [info] 检测数据: num_detections=0, num_segments=0
[09-10 10:29:04.614] [info] FFT和俯仰角计算完成, 得到 0 个结果：
[09-10 10:29:04.615] [info] 跟踪结果数量: 0
[09-10 10:29:04.615] [info] Track[ 0] ID:  0 | Pos: (1089.80,  512.06,  118.05) m | Vel: (-10.06,  -4.72,  -1.09) m/s | R: 1209.88 m | Vr: -11.17 m/s | Az:  25.17° | El:   5.60°
[09-10 10:29:04.615] [info] Track[ 1] ID:  1 | Pos: (1087.44,  520.93,   99.16) m | Vel: (-10.37,  -4.96,  -0.95) m/s | R: 1209.84 m | Vr: -11.53 m/s | Az:  25.60° | El:   4.70°
[09-10 10:29:04.615] [info] 跟踪完成，输出 2 个轨迹
[09-10 10:29:04.615] [info] 已释放内部检测数据内存
[09-10 10:29:04.615] [info] - 显存 - 目标跟踪完成: 使用 833.1 MB
[09-10 10:29:04.615] [info] - 内存 - 目标跟踪完成: 使用 500.7 MB
[09-10 10:29:04.695] [info] =====开始目标检测=====
[09-10 10:29:05.885] [info] 提取到 1024 个有效帧头
[09-10 11:10:17.105] [info] =====算法库初始化开始=====
[09-10 11:10:17.105] [info] Initializing unified resource manager...
[09-10 11:10:17.105] [info] Initializing GPU resource manager...
[09-10 11:10:17.186] [info] GPU resource manager initialized successfully
[09-10 11:10:17.186] [info] Initializing memory pool manager...
[09-10 11:10:17.186] [info] CUDA memory pool created with max size: 512MB
[09-10 11:10:17.186] [info] Host memory pool created with max size: 1024MB
[09-10 11:10:17.186] [info] Memory pool manager initialized successfully
[09-10 11:10:17.186] [info] Initializing FFT GPU Optimizer with provided GPU manager: 1024x2048
[09-10 11:10:17.186] [info] Initializing FFT resource pools for 5 segment lengths
[09-10 11:10:17.187] [info] Resource pool initialized for length 512: 10 buffers, 10 plans
[09-10 11:10:17.188] [info] Resource pool initialized for length 256: 10 buffers, 10 plans
[09-10 11:10:17.188] [info] Resource pool initialized for length 128: 10 buffers, 10 plans
[09-10 11:10:17.188] [info] Resource pool initialized for length 64: 10 buffers, 10 plans
[09-10 11:10:17.188] [info] All FFT resource pools initialized successfully
[09-10 11:10:17.188] [info] FFT GPU Optimizer initialized successfully with provided GPU manager
[09-10 11:10:17.188] [info] Unified resource manager initialized successfully
[09-10 11:10:17.189] [info] Loading TensorRT engine from: data/0908-fp16.trt
[09-10 11:10:17.202] [info] TensorRT engine loaded successfully: data/0908-fp16.trt
[09-10 11:10:17.202] [info] Engine info: 2 bindings
[09-10 11:10:17.202] [info]   Binding 0: input [1x1x1024x1024x2]
[09-10 11:10:17.202] [info]   Binding 1: output [1x1x512x1024]
[09-10 11:10:17.202] [info] 模型输入维度: [1x1024x1024x2]
[09-10 11:10:17.203] [info] GPU buffers allocated: input=8 MB, output=2 MB
[09-10 11:10:17.206] [info] 加载俯仰角查表数据: data/hecha_table.csv
[09-10 11:10:17.206] [info] - 显存 - 算法初始化完成: 使用 899.1 MB
[09-10 11:10:17.206] [info] - 内存 - 算法初始化完成: 使用 259.2 MB
[09-10 11:10:17.206] [info] =====算法库初始化完成=====
[09-10 11:10:17.295] [info] =====开始目标检测=====
[09-10 11:10:17.295] [info] 提取到 1024 个有效帧头
[09-10 11:10:17.380] [warning] 无效帧:302 模型没有检测到目标，跳过处理
