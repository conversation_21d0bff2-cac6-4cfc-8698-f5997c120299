#!/bin/bash

# 优化的 Valgrind 测试脚本
# 使用抑制文件来过滤第三方库的已知内存泄漏

echo "=== 运行优化的 Valgrind 内存泄漏检测 ==="

# 设置环境变量以减少第三方库的内存使用
export CUDA_CACHE_DISABLE=1
export OMP_NUM_THREADS=1
export OPENCV_NUM_THREADS=1

# 编译项目
echo "编译项目..."
cd build
make -j$(nproc)
if [ $? -ne 0 ]; then
    echo "编译失败"
    exit 1
fi

# 运行 Valgrind 检测
echo "运行 Valgrind 内存泄漏检测..."
valgrind \
    --tool=memcheck \
    --leak-check=full \
    --show-leak-kinds=all \
    --track-origins=yes \
    --verbose \
    --suppressions=../valgrind.supp \
    --log-file=../valgrind_optimized.log \
    ./test_algorithm

echo "Valgrind 检测完成，结果保存在 valgrind_optimized.log"

# 分析结果
echo ""
echo "=== 内存泄漏摘要 ==="
grep "LEAK SUMMARY" -A 10 ../valgrind_optimized.log

echo ""
echo "=== 检查是否还有 definitely lost ==="
definitely_lost=$(grep "definitely lost:" ../valgrind_optimized.log | awk '{print $4}')
if [ "$definitely_lost" = "0" ]; then
    echo "✓ 没有 definitely lost 内存泄漏"
else
    echo "✗ 仍有 definitely lost 内存泄漏: $definitely_lost bytes"
fi

echo ""
echo "=== 检查 possibly lost 是否减少 ==="
possibly_lost=$(grep "possibly lost:" ../valgrind_optimized.log | awk '{print $4}')
echo "Possibly lost: $possibly_lost bytes"

echo ""
echo "=== 检查 still reachable 是否在合理范围 ==="
still_reachable=$(grep "still reachable:" ../valgrind_optimized.log | awk '{print $4}')
echo "Still reachable: $still_reachable bytes"

# 如果 still reachable 超过 2MB，给出警告
if [ "$still_reachable" -gt 2097152 ]; then
    echo "⚠️  Still reachable 内存较多，可能需要进一步优化"
else
    echo "✓ Still reachable 内存在合理范围内"
fi

echo ""
echo "完整报告请查看: valgrind_optimized.log"
