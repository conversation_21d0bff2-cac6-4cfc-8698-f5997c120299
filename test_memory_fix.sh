#!/bin/bash

echo "编译内存损坏修复测试程序..."

# 设置编译参数
CXX=g++
CXXFLAGS="-std=c++17 -O2 -Wall -Wextra"
INCLUDES="-Iinclude -Isrc"
LIBS="-lspdlog -lpthread"

# 编译测试程序
$CXX $CXXFLAGS $INCLUDES -o test_memory_corruption_fix \
    test/test_memory_corruption_fix.cpp \
    src/libSCR_5000_Alg.cpp \
    $LIBS

if [ $? -eq 0 ]; then
    echo "编译成功，运行测试..."
    ./test_memory_corruption_fix
else
    echo "编译失败"
    exit 1
fi
