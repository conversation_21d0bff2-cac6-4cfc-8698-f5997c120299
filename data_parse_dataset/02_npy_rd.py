import math
from matplotlib import gridspec
import numpy as np
import matplotlib.pyplot as plt
import os
import scipy
from scipy.io import loadmat
from scipy.signal import windows
from PIL import Image
from scipy.ndimage import gaussian_filter, median_filter
from scipy.signal import stft
import mkl_fft
from tqdm import tqdm
import re

# 定义一个函数用于从路径中提取数字
def extract_number(path):
    match = re.search(r'\d+', path)
    if match:
        return int(match.group())
    return 0

def StGet2(Rbin):
    fs = 50e6  # 采样率
    PRI = 50e-6  # 脉冲重复间隔
    Pulse_Width_1 = 9e-6  # 信号1持续时间
    Pulse_Width_2 = 1e-6  # 信号2持续时间
    B_Width = 12.5e6  # 信号带宽
    
    t1 = np.arange(0, Pulse_Width_1, 1/fs)
    t2 = np.arange(0, Pulse_Width_2, 1/fs)
    
    Signal_element_baseband_1 = np.exp(-1j * 2 * np.pi * (-1 * B_Width + 0.5 * B_Width / Pulse_Width_1 * t1) * t1)
    Signal_element_baseband_2 = np.exp(-1j * 2 * np.pi * (0 * B_Width + 0.5 * B_Width / Pulse_Width_2 * t2) * t2)
    
    Signal_element_baseband = np.concatenate((Signal_element_baseband_1, Signal_element_baseband_2)) 
    
    win1 = windows.hamming(len(Signal_element_baseband))
    Signal_element_baseband = Signal_element_baseband / np.max(np.abs(Signal_element_baseband))
    St_fft_1 = np.fft.fft(Signal_element_baseband * win1, Rbin)  # FFT变换

    win2 = windows.hamming(len(Signal_element_baseband_1))
    Signal_element_baseband_1 = Signal_element_baseband_1 / np.max(np.abs(Signal_element_baseband_1))
    St_fft_2 = np.fft.fft(Signal_element_baseband_1 * win2, Rbin)

    return St_fft_1, St_fft_2

def PC(Sin, St, range_window=None, doppler_window=None):
    N, M = Sin.shape  # 获取信号的行数与列数
    Rbin = len(St)    # 参考信号的长度

    SigRin = np.zeros((N, M), dtype=np.complex64)  # 初始化输出矩阵

    if range_window is None:
        range_window = (0.54 - 0.46 * np.cos((2 * math.pi * np.arange(N)) / (N - 1)))

    if doppler_window is None:
        doppler_window = (0.54 - 0.46 * np.cos((2 * math.pi * np.arange(M)) / (M - 1)))

    for mm in range(M):
        STEMP = Sin[:, mm]  # 获取第 mm 列信号
        STEMP_windowed = STEMP * range_window
        STEMP_fft = np.fft.fft(STEMP_windowed, Rbin)  # 对该信号进行 FFT 变换
        SigRin[:, mm] = np.fft.ifft(STEMP_fft * np.conj(St))

    SigRin_windowed = SigRin * doppler_window

    return SigRin_windowed

def CLEAN(data, V_min, V_max, fs, i):
    data_temp = data.copy()
    Doppler_num, Rbin = data.shape

    fft_data_temp = np.fft.fftshift(np.fft.fft(data_temp, axis=0), axes=0)
    Doppler_restrain = np.array([Doppler_num//2 + 1 - V_min, 
                                 Doppler_num//2 + 1 + V_max])

    iteration_num = 5

    for _ in range(iteration_num):
        max_amplitude = np.max(np.abs(fft_data_temp[Doppler_restrain[0]:Doppler_restrain[1], :]), axis=0)
        aa = np.argmax(np.abs(fft_data_temp[Doppler_restrain[0]:Doppler_restrain[1], :]), axis=0)
        omiga = (aa - np.ceil((Doppler_restrain[1]-Doppler_restrain[0])/2) - 1)/Doppler_num * fs

        cn = np.zeros((Doppler_num, Rbin), dtype=np.complex128)
        for j in range(Rbin):
            idx_in_restrain = aa[j]
            original_idx = idx_in_restrain + Doppler_restrain[0] - 1
            fai = np.angle(fft_data_temp[original_idx, j])
            cn[:, j] = (max_amplitude[j]/Doppler_num) * np.exp(
                1j*(2*np.pi*omiga[j]*np.arange(Doppler_num)/fs + fai))

        data_temp = data_temp - cn
        fft_data_temp = np.fft.fft(data_temp, axis=0)

    Data_after_CLEAN = fft_data_temp
    return Data_after_CLEAN

def mean_subtract(data):
    col_means = np.mean(data, axis=0)
    data_mean_subtracted = data - col_means
    return data_mean_subtracted

def zero_middle_columns(data):
    _, num_cols = data.shape
    mid_col = num_cols // 2
    result = data.copy()
    result[:, mid_col-1:mid_col+2] = np.ones((result.shape[0], 3)) * 5
    return result

def apply_hanning_window(data, numSamplePerChirp, numChirps):
    hanningWindowRange = (0.54 - 0.46 * np.cos(((2 * math.pi * np.arange(numSamplePerChirp)) / (numSamplePerChirp - 1))))
    hanningWindowDoppler = (0.54 - 0.46 * np.cos(((2 * math.pi * np.arange(numChirps)) / (numChirps - 1))))

    range_fft_coef = np.repeat(np.expand_dims(hanningWindowRange, 0), repeats=numChirps, axis=0)
    doppler_fft_coef = np.repeat(np.expand_dims(hanningWindowDoppler, 0).transpose(), repeats=numSamplePerChirp, axis=1)

    return np.multiply(data, doppler_fft_coef)

def save_image(data, file_path, dpi=300, cmap='gray', origin='upper'):
    plt.figure(figsize=(data.shape[1] / dpi, data.shape[0] / dpi), dpi=dpi)
    plt.imshow(data, cmap=cmap, aspect='auto', origin=origin)
    plt.axis('off')
    plt.savefig(file_path, bbox_inches='tight', pad_inches=0, dpi=dpi)
    plt.close()

def resize_and_save_image(img, file_path, new_size, algorithm=Image.LANCZOS):
    img = img.resize(new_size, algorithm)
    img.save(file_path)

def save_frequency_profile(data, file_path, dpi=300):
    plt.figure(figsize=(data.shape[0] / dpi, data.shape[1] / dpi), dpi=dpi)
    doppler_profile = np.max(data, axis=1)
    plt.plot(doppler_profile)
    
    ax = plt.gca()
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['bottom'].set_visible(False)
    ax.spines['left'].set_visible(False)
    ax.set_title('')
    ax.set_xticks([])
    ax.set_yticks([])
    ax.xaxis.set_ticklabels([])
    ax.yaxis.set_ticklabels([])
    
    plt.savefig(file_path, bbox_inches='tight', pad_inches=0, dpi=dpi)
    plt.close()

def concatenate_images_vertically(images):
    widths, heights = zip(*(img.size for img in images))
    total_height = sum(heights)
    max_width = max(widths)
    
    combined_img = Image.new('RGB', (max_width, total_height))
    y_offset = 0
    for img in images:
        combined_img.paste(img, (0, y_offset))
        y_offset += img.height
    return combined_img

def process_file(adc_file, npy_folder, adc_output_folder, numSamplePerChirp, numChirps):
    adc_path = os.path.join(npy_folder, adc_file)
    data = np.load(adc_path)
    
    start_idx = 0
    compressed_data = []

    for num in numChirps:
        end_idx = start_idx + num
        sub_matrix = data[start_idx:end_idx]
        compressed_data.append(sub_matrix)
        start_idx = end_idx

    for i, sub_data in enumerate(compressed_data):
        windowed_data = apply_hanning_window(sub_data, numSamplePerChirp, numChirps[i])
        rd_spectrum = mkl_fft.fft(windowed_data, numChirps[i], axis=0)
        
        rd = np.log10(np.abs(rd_spectrum)+1e-10)
        rd_spectrum_magnitude = np.log10(np.abs(rd_spectrum) + 1e-10)
        
        rd_min, rd_max = np.min(rd_spectrum_magnitude), np.max(rd_spectrum_magnitude)
        rd_spectrum_magnitude = (rd_spectrum_magnitude - rd_min) / (rd_max - rd_min)
        
        threshold = np.mean(rd_spectrum_magnitude) + 1.7 * np.std(rd_spectrum_magnitude)
        signal_mask = rd_spectrum_magnitude > threshold
        noise_reduction = 0.0001
        rd_spectrum_magnitude[~signal_mask] *= noise_reduction
        rd_spectrum_magnitude = median_filter(rd_spectrum_magnitude, size=3)
        
        name = adc_file.split('.')[0]
        
        rd_path = os.path.join(adc_output_folder, f"{name}_part_{i+1}.png")
        save_image(rd, rd_path, cmap='viridis')
        
        img_rd = Image.open(rd_path)
        resize_and_save_image(img_rd, rd_path, (numSamplePerChirp, numChirps[i]))
        
        rd_new_output_path = os.path.join(adc_output_folder, f"{name}_new_part_{i+1}.png")
        save_image(rd_spectrum_magnitude, rd_new_output_path)
        
        img_new_rd = Image.open(rd_new_output_path)
        resize_and_save_image(img_new_rd, rd_new_output_path, (numSamplePerChirp, numChirps[i]))
        
        freq_output_path = os.path.join(adc_output_folder, f"{name}_freq_part_{i+1}.png")
        save_frequency_profile(rd, freq_output_path)
        
        img_freq = Image.open(freq_output_path)
        img_freq = img_freq.rotate(-90, expand=True)
        resize_and_save_image(img_freq, freq_output_path, (numSamplePerChirp, numChirps[i]))
        
        cleaned_data = CLEAN(sub_data, 3, 3, 50e6, i)
        rd_clean = np.log10(np.abs(cleaned_data) + 1e-10)
        
        rd_clean_output_path = os.path.join(adc_output_folder, f"{name}_clean_part_{i+1}.png")
        save_image(rd_clean, rd_clean_output_path, cmap='viridis')
        
        img_clean = Image.open(rd_clean_output_path)
        resize_and_save_image(img_clean, rd_clean_output_path, (numSamplePerChirp, numChirps[i]))

def main():
    npy_folder = '/media/llh/新加卷/采集数据/有目标数据整理/augmented/npy'
    adc_output_folder = '/media/llh/新加卷/采集数据/有目标数据整理/augmented/rd'
    os.makedirs(adc_output_folder, exist_ok=True)

    numSamplePerChirp = 2048
    numChirps = [512, 256, 128, 64, 64]

    npy_files = [f for f in os.listdir(npy_folder) if f.endswith('.npy')]
    npy_files.sort(key=extract_number)
    npy_files = [f for f in npy_files if extract_number(f) >= 0]

    for adc_file in tqdm(npy_files, desc="Processing files", unit="file"):
        process_file(adc_file, npy_folder, adc_output_folder, numSamplePerChirp, numChirps)

if __name__ == '__main__':
    main()