#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
空白掩膜生成程序
"""

import os
import sys
import argparse
from pathlib import Path
import numpy as np
from PIL import Image

IMG_WIDTH = 1024
IMG_HEIGHT = 512

# 识别为“图片文件名”的扩展名（可按需增减）
IMAGE_EXTS = {".png", ".jpg", ".jpeg", ".bmp", ".tif", ".tiff", ".webp"}


def is_image_filename(path: Path) -> bool:
    return path.suffix.lower() in IMAGE_EXTS


def ensure_unique_path(p: Path) -> Path:
    """
    如果目标文件已存在，则在文件名后追加 _1, _2, ... 以避免覆盖。
    """
    if not p.exists():
        return p
    stem, suffix = p.stem, p.suffix
    i = 1
    while True:
        candidate = p.with_name(f"{stem}_{i}{suffix}")
        if not candidate.exists():
            return candidate
        i += 1


def generate_black_png(save_path: Path, width: int = IMG_WIDTH, height: int = IMG_HEIGHT) -> None:
    """
    生成全黑的 8-bit 灰度 PNG。PNG 本质为无符号 8-bit（uint8）。
    """
    save_path.parent.mkdir(parents=True, exist_ok=True)
    arr = np.zeros((height, width), dtype=np.uint8)  # 全黑
    im = Image.fromarray(arr, mode="L")  # L = 8-bit 灰度
    im.save(save_path, format="PNG")

def process(source_dir: Path, dest_dir: Path, verbose: bool = True) -> None:
    """
    遍历 source_dir 的所有子目录：
    - 找到子目录里的图片文件名
    - 在 dest_dir 下生成同名全黑 PNG（不保留子目录结构）
    """
    if not source_dir.exists() or not source_dir.is_dir():
        raise FileNotFoundError(f"源目录不存在或不是目录：{source_dir}")

    # 预创建一个黑图模板（可重复使用以略微减少内存分配开销）
    black_template = np.zeros((IMG_HEIGHT, IMG_WIDTH), dtype=np.uint8)

    created_count = 0
    skipped_count = 0

    for root, dirs, files in os.walk(source_dir):
        for fname in files:
            src_file = Path(root) / fname
            if not is_image_filename(src_file):
                continue

            # 输出文件直接放到目标目录
            base_stem = src_file.stem  # 去掉扩展名，统一输出为 .png
            out_path = dest_dir / f"{base_stem}.png"

            # 避免覆盖已存在的同名文件
            out_path = ensure_unique_path(out_path)

            # 生成图片
            try:
                dest_dir.mkdir(parents=True, exist_ok=True)
                Image.fromarray(black_template, mode="L").save(out_path, format="PNG")
                created_count += 1
                if verbose:
                    print(f"[OK] {out_path}")
            except Exception as e:
                skipped_count += 1
                print(f"[ERR] 无法创建 {out_path}: {e}", file=sys.stderr)

    if verbose:
        print(f"\n完成：创建 {created_count} 个 PNG，跳过/失败 {skipped_count} 个。")


def main():
    parser = argparse.ArgumentParser(
        description="根据源目录子文件夹内图片文件名，生成同名的全黑 PNG（1024x512，8-bit），保存到目标目录。"
    )
    parser.add_argument("--source_dir", type=str, default="/media/llh/新加卷/采集数据/0905数据处理/rd" ,help="源目录（遍历其所有子文件夹与图片文件名）")
    parser.add_argument("--dest_dir", type=str, default="/media/llh/新加卷/采集数据/0905数据处理/mask" ,help="目标目录（按相同子目录结构输出 PNG）")
    parser.add_argument("--quiet", action="store_true", help="减少输出日志")
    args = parser.parse_args()

    source_dir = Path(args.source_dir).resolve()
    dest_dir = Path(args.dest_dir).resolve()

    process(source_dir, dest_dir, verbose=not args.quiet)


if __name__ == "__main__":
    main()
