import os
import numpy as np
from concurrent.futures import ProcessPoolExecutor
from tqdm import tqdm
import pickle

class OnlineStats:
    """增量统计计算器（Welford算法）"""
    def __init__(self):
        self.count = 0
        self.mean = 0.0
        self.M2 = 0.0
        self.min = float('inf')
        self.max = -float('inf')

    def update(self, arr):
        """更新统计量"""
        arr = arr.ravel()
        if arr.size == 0:
            return
        
        self.min = min(self.min, np.min(arr))
        self.max = max(self.max, np.max(arr))

        batch_count = arr.size
        batch_mean = np.mean(arr)
        batch_M2 = np.sum((arr - batch_mean) ** 2)

        delta = batch_mean - self.mean
        total_count = self.count + batch_count

        self.mean = (self.count * self.mean + batch_count * batch_mean) / total_count
        self.M2 += batch_M2 + delta ** 2 * self.count * batch_count / total_count
        self.count = total_count

    @property
    def std(self):
        return np.sqrt(self.M2 / self.count) if self.count > 0 else 0

    def merge(self, other):
        """合并另一个 OnlineStats 对象"""
        if other.count == 0:
            return self
        if self.count == 0:
            return other

        merged = OnlineStats()
        merged.min = min(self.min, other.min)
        merged.max = max(self.max, other.max)

        delta = other.mean - self.mean
        total_count = self.count + other.count

        merged.mean = (self.count * self.mean + other.count * other.mean) / total_count
        merged.M2 = self.M2 + other.M2 + delta ** 2 * self.count * other.count / total_count
        merged.count = total_count
        return merged

def process_file_stats(file_path, process_func=None):
    """在子进程中处理文件并返回统计对象"""
    stats_real = OnlineStats()
    stats_imag = OnlineStats()
    try:
        data = np.load(file_path, mmap_mode='r')  # mmap模式更省内存
        if process_func:
            data = process_func(data)
        stats_real.update(data.real)
        stats_imag.update(data.imag)
        return stats_real, stats_imag
    except Exception as e:
        print(f"[Error] Failed processing {file_path}: {e}")
        return None, None

def count_radar_data_optimized(radar_data_path, workers=8, cache_path=None):
    """主函数：并行统计雷达数据"""
    # 文件列表
    files = sorted([
        os.path.join(radar_data_path, f)
        for f in os.listdir(radar_data_path) if f.endswith('.npy')
    ])

    if cache_path and os.path.exists(cache_path):
        with open(cache_path, 'rb') as f:
            real_stats, imag_stats = pickle.load(f)
        print("已加载缓存统计结果。")
    else:
        real_stats = OnlineStats()
        imag_stats = OnlineStats()

        with ProcessPoolExecutor(max_workers=workers) as executor:
            futures = executor.map(process_file_stats, files)

            for result in tqdm(futures, total=len(files), desc="Processing"):
                if result is None:
                    continue
                r_stat, i_stat = result
                real_stats = real_stats.merge(r_stat)
                imag_stats = imag_stats.merge(i_stat)

        if cache_path:
            with open(cache_path, 'wb') as f:
                pickle.dump((real_stats, imag_stats), f)
            print(f"统计结果已缓存至：{cache_path}")

    # 打印统计结果
    print("\n实部统计:")
    print(f"最大值: {real_stats.max:.6f}")
    print(f"最小值: {real_stats.min:.6f}")
    print(f"平均值: {real_stats.mean:.6f}")
    print(f"标准差: {real_stats.std:.6f}")

    print("\n虚部统计:")
    print(f"最大值: {imag_stats.max:.6f}")
    print(f"最小值: {imag_stats.min:.6f}")
    print(f"平均值: {imag_stats.mean:.6f}")
    print(f"标准差: {imag_stats.std:.6f}")

if __name__ == "__main__":
    radar_data_path = '/media/llh/新加卷/采集数据/0905数据处理/npy'
    cache_file = '/home/<USER>/My_Project/MSHNet_TensorRT_Test/data_parse_dataset/radar_stats_cache.pkl'
    count_radar_data_optimized(radar_data_path, workers=8, cache_path=cache_file)
