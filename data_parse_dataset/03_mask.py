import os
import cv2
import numpy as np
from tqdm import tqdm

def yolo_to_pixel_coords(x, y, img_width, img_height):
    x_center = int(x * img_width)
    y_center = int(y * img_height)
    # width = int(w * img_width)
    # height = int(h * img_height)
    return x_center, y_center

def draw_gaussian_elliptical_spot(mask, x_center, y_center, sigma_x=10, sigma_y=3, theta=0):
    # 定义高斯参数（与原椭圆尺寸匹配）
    sigma_x = 12 / 3  # 原椭圆长轴2对应3σ范围
    sigma_y = 6 / 3  # 原椭圆短轴1对应3σ范围
    
    # 生成坐标网格
    x = np.arange(-3*sigma_x, 3*sigma_x + 1e-9, 1)
    y = np.arange(-3*sigma_y, 3*sigma_y + 1e-9, 1)
    x, y = np.meshgrid(x, y)
    
    # 计算二维高斯分布
    g = np.exp(-(x**2/(2*sigma_x**2) + y**2/(2*sigma_y**2)))
    g = (g / g.max() * 255).astype(np.uint8)  # 归一化到0-255
    
    # 确定覆盖区域
    y_start = y_center - g.shape[0]//2
    y_end = y_center + (g.shape[0]+1)//2
    x_start = x_center - g.shape[1]//2
    x_end = x_center + (g.shape[1]+1)//2
    
    # 处理边界情况
    mask_h, mask_w = mask.shape
    y_start = max(y_start, 0)
    y_end = min(y_end, mask_h)
    x_start = max(x_start, 0)
    x_end = min(x_end, mask_w)
    
    if y_start >= y_end or x_start >= x_end:
        return mask
    
    # 裁剪高斯核
    g = g[(y_start - (y_center - g.shape[0]//2)) : 
          (g.shape[0] - ((y_center + (g.shape[0]+1)//2) - y_end)),
          (x_start - (x_center - g.shape[1]//2)) : 
          (g.shape[1] - ((x_center + (g.shape[1]+1)//2) - x_end))]
    
    # 叠加高斯权重（保留最大值）
    region = mask[y_start:y_end, x_start:x_end]
    mask[y_start:y_end, x_start:x_end] = np.maximum(region, g)
    
    return mask


def draw_elliptical_spot(mask, x_center, y_center):
    # 定义椭圆参数（横向小椭圆）
    axis_length = (5, 2)  # 长轴像素，短轴像素
    cv2.ellipse(mask, 
               (x_center, y_center),
               axis_length,
               0, 0, 360,  # 角度参数
               255, -1)    # 颜色和填充
    return mask

def draw_rectangular_spot(mask, x_center, y_center, width=10, height=4):
    """
    绘制矩形斑点（替代原来的椭圆）
    Args:
        mask: 输入的掩膜（单通道）
        x_center, y_center: 矩形中心坐标
        width: 矩形的宽度（横向像素）
        height: 矩形的高度（纵向像素）
    """
    # 计算矩形的左上角和右下角坐标
    x1 = int(x_center - width // 2)
    y1 = int(y_center - height // 2)
    x2 = int(x_center + width // 2)
    y2 = int(y_center + height // 2)

    # 确保坐标在图像范围内
    h, w = mask.shape
    x1 = max(0, x1)
    y1 = max(0, y1)
    x2 = min(w, x2)
    y2 = min(h, y2)

    # 绘制填充矩形（颜色=255，填充=-1）
    cv2.rectangle(mask, (x1, y1), (x2, y2), 255, -1)
    
    return mask

def process_images(label_yolo_dir,mask_images_dir):
    # 创建保存mask图像的目录
    os.makedirs(mask_images_dir, exist_ok=True)
    
    # 创建统一尺寸的二值图像模板
    img_width = 1024
    img_height = 512
    
    txt_files = [f for f in os.listdir(label_yolo_dir) if f.endswith('.txt')]


    for txt_file in tqdm(txt_files,desc="Processing files", unit="file"):
        if not txt_file.endswith('.txt'):
            continue
            
        # 初始化全黑mask
        mask = np.zeros((img_height, img_width), dtype=np.uint8)
        
        txt_path = os.path.join(label_yolo_dir, txt_file)
        with open(txt_path, "r") as f:
            for line in f:
                data = line.strip().split()
                if len(data) == 3:
                    # 解析YOLO格式坐标
                    x, y = map(float, data[1:])
                    # 转换坐标
                    x_center, y_center = yolo_to_pixel_coords(x, y, img_width, img_height)
                    # 绘制椭圆
                    # mask = draw_elliptical_spot(mask, x_center, y_center)
                    mask = draw_rectangular_spot(mask, x_center, y_center)
        # 保存mask文件（保持与txt文件同名）
        mask_filename = os.path.splitext(txt_file)[0] + '.png'
        mask_path = os.path.join(mask_images_dir, mask_filename)
        cv2.imwrite(mask_path, mask)

if __name__ == "__main__":
    label_yolo_dir = '/media/llh/新加卷/采集数据/0905数据处理/label'
    mask_images_dir = '/media/llh/新加卷/采集数据/0905数据处理/mask'
    process_images(label_yolo_dir,mask_images_dir)