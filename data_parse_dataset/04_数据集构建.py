#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import shutil
from pathlib import Path


def copy_if_exists(src_file: Path, dst_dir: Path, out_name=None, verbose=True):
    """如果文件存在则复制到目标文件夹；若目标已存在则跳过"""
    if src_file.exists():
        dst_dir.mkdir(parents=True, exist_ok=True)
        dst_file = dst_dir / (out_name if out_name else src_file.name)
        if dst_file.exists():
            if verbose:
                print(f"[SKIP] {dst_file} 已存在，跳过")
            return False
        shutil.copy2(src_file, dst_file)
        if verbose:
            print(f"[OK] {src_file} -> {dst_file}")
        return True
    return False


def process(mask_dir, source_dirs, verbose=True):
    if not mask_dir.exists() or not mask_dir.is_dir():
        raise FileNotFoundError(f"mask 路径不存在: {mask_dir}")

    # 输出目录（在 mask 同级）
    base_dir = mask_dir.parent
    out_rd = base_dir / "rd"
    out_bin = base_dir / "bin"
    out_npy = base_dir / "npy"

    # 遍历 mask 下所有 png
    mask_files = list(mask_dir.glob("*.png"))
    print(f"发现 {len(mask_files)} 个 mask PNG 文件")

    copied_rd = copied_bin = copied_npy = 0

    for mask_file in mask_files:
        stem = mask_file.stem  # 文件名（无扩展名）

        for src_root in source_dirs:
            if not src_root.exists():
                continue

            # 遍历所有子目录
            for sub in src_root.glob("*"):
                if not sub.is_dir():
                    continue

                merge_dir = sub / "合并"
                reorder_dir = sub / "重排"
                npy_dir = sub / "npy"

                # --- 合并目录：要进入子文件夹，文件名加 _combined ---
                if merge_dir.exists():
                    for merge_sub in merge_dir.glob("*"):
                        if not merge_sub.is_dir():
                            continue
                        png_src = merge_sub / f"{stem}_combined.png"
                        if copy_if_exists(png_src, out_rd, out_name=f"{stem}.png", verbose=verbose):
                            copied_rd += 1

                # --- 重排目录：直接找 .bin ---
                bin_src = reorder_dir / f"{stem}.bin"
                if copy_if_exists(bin_src, out_bin, verbose=verbose):
                    copied_bin += 1

                # --- npy 目录：直接找 .npy ---
                npy_src = npy_dir / f"{stem}.npy"
                if copy_if_exists(npy_src, out_npy, verbose=verbose):
                    copied_npy += 1

    print(f"\n复制完成：")
    print(f"  PNG -> rd: {copied_rd}")
    print(f"  BIN -> bin: {copied_bin}")
    print(f"  NPY -> npy: {copied_npy}")


if __name__ == "__main__":
    # ===== 在这里直接写路径 =====
    mask_dir = Path("/media/llh/新加卷/采集数据/0905数据处理/mask")
    source_dirs = [
        Path("/media/llh/新加卷/采集数据/0812(测试)"),
        Path("/media/llh/新加卷/采集数据/0905_1"),
        Path("/media/llh/新加卷/采集数据/0905_2"),
        Path("/media/llh/新加卷/采集数据/0905_3"),
        Path("/media/llh/新加卷/采集数据/0905_4")
    ]

    process(mask_dir, source_dirs, verbose=True)
