#!/bin/sh

# 定义可执行程序路径
executable="./data_parse"

# 定义路径
OUTPUT_PATH="/home/<USER>/app/data/output_radar_data"
INPUT_PATH="/home/<USER>/app/data/targetData"

# 定义方位角范围
min_angle="0"
max_angle="90"

# 定义保存的数据
S_head="1"
S_data="1"
D_head="1"
D_data="1"

# 定义远程主机信息
REMOTE_IP="**************"
REMOTE_USER="llh"

REMOTE_PATH="/media/llh/新加卷/采集数据/0812"

# REMOTE_PATH="E:\\SCR5000\\data\\0903"

# 运行数据解析（提取和通道/差通道数据）
echo "开始解析雷达数据..."
$executable "$INPUT_PATH" "$OUTPUT_PATH" "$min_angle" "$max_angle" "$S_head" "$S_data" "$D_head" "$D_data"

# 检查可执行程序是否成功运行
if [ $? -ne 0 ]; then
    echo "雷达数据解析失败，退出脚本。"
    exit 1
fi

# 检查输出目录是否存在
if [ ! -d "$OUTPUT_PATH" ]; then
    echo "错误: 输出目录 $OUTPUT_PATH 不存在"
    exit 1
fi

# 1. 文件传输功能
echo "正在将文件从 $OUTPUT_PATH 传输到远程主机..."

read -p "确定要继续吗？(y/n): " choice
if [ "$choice" = "y" ]; then
    scp -r "$OUTPUT_PATH"/* "$REMOTE_USER@$REMOTE_IP:$REMOTE_PATH"
else
    echo "跳过传输"
fi

if [ $? -ne 0 ]; then
    echo "警告: 文件传输失败"
    exit 1
fi

# 2. 清理输出目录
echo "正在清理输出目录 $OUTPUT_PATH..."
rm -rf "$OUTPUT_PATH"/*

if [ $? -eq 0 ]; then
    echo "成功清理输出目录"
else
    echo "警告: 清理输出目录时出错"
fi

# 3. 是否清理输入目录的.bin文件
read -p "是否清理输入目录中的.bin文件？(y/n): " choice
if [ "$choice" = "y" ]; then
echo "正在清理输入目录中的.bin文件..."
find "$INPUT_PATH" -name '*data*.bin' -type f -delete

if [ $? -eq 0 ]; then
    echo "成功清理输入目录中的.bin文件"
else
    echo "警告: 清理输入目录中的.bin文件时出错"
fi
fi

echo "文件传输和清理操作完成"
