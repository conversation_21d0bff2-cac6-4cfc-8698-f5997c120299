#include <iostream>
#include <fstream>
#include <filesystem>
#include <vector>
#include <deque>
#include <sstream>
#include <cstring>
#include <mutex>
#include <thread>
#include <atomic>
#include <queue>
#include <algorithm>
#include <chrono>
#include <memory>
#include <array>

namespace fs = std::filesystem;

// 常量定义
const uint32_t FRAME_HEADER_MARKER = 0xAAAA5555;    // 帧头标识
const size_t HEADER_SIZE = 40;                      // 帧头大小
const size_t SAMPLE_DATA_SIZE = 4 * 4096;           // 每通道4字节 × 4096点
const size_t FLOAT_COUNT_PER_PULSE = SAMPLE_DATA_SIZE / sizeof(float);  // 每脉冲float数量
const size_t MAX_PULSE_BUFFER_SIZE = 3000;          // 脉冲缓冲区最大大小
const int MAX_THREADS = 4;                          // 增加最大线程数限制
const size_t PULSE_GROUP_SIZE = 1024;               // 脉冲组大小

// 数据保存选项
struct SaveOptions {
    bool save_sum_header = true;                    // 保存和通道1024个脉冲帧头
    bool save_sum_data = true;                      // 保存和通道重排数据
    bool save_diff_header = true;                   // 保存差通道1024个脉冲帧头
    bool save_diff_data = true;                     // 保存差通道重排数据
};

// 配置选项
struct ProcessingOptions {
    bool save_csv = true;                           // 是否保存CSV帧头信息
    bool verbose_log = false;                       // 详细日志输出
    size_t io_buffer_size = 8192;                   // I/O缓冲区大小(字节)
    SaveOptions save_opts;                          // 数据保存选项
};

struct FrameHeader
{
    uint32_t marker;
    uint16_t channel;
    uint16_t circle_num;
    uint16_t frame_num;
    uint8_t  task_type;
    uint8_t  group_id;
    uint16_t group_limit;
    uint16_t pulse_id;
    uint8_t  waveform;
    uint8_t  waveform_type;
    uint16_t frame_length;
    uint32_t angle; // 方位角*100
    uint16_t angle_counter;
    char     reserved[14];
};


// 优化的脉冲数据结构 - 使用固定大小数组避免动态分配
struct Pulse {
    FrameHeader header;                           // 原始帧头
    std::array<char, SAMPLE_DATA_SIZE> samples;   // 固定大小样本数组

    Pulse() = default;
    Pulse(const FrameHeader& h, const char* data) : header(h) {
        std::memcpy(samples.data(), data, SAMPLE_DATA_SIZE);
    }
};

// 写入CSV文件表头
void write_csv_header(std::ostream& out) {
    out << "通道,圈号,帧号,脉冲编号,波位,波位类型,帧长度,方向角,方向角更新计数器\n";
}

// 追加CSV数据行
void append_csv_row(std::ostringstream& out, const FrameHeader& h) {
    out << h.channel << ","
        << h.circle_num << ","
        << h.frame_num << ","
        << h.pulse_id << ","
        << static_cast<int>(h.waveform) << ","
        << static_cast<int>(h.waveform_type) << ","
        << h.frame_length << ","
        << h.angle << ","
        << h.angle_counter << "\n";
}

// 全局缓存重排索引，避免重复计算
static std::vector<int> g_rearrange_indices;
static std::once_flag g_indices_flag;

/**
 * 生成重排索引（优化版本 - 使用缓存）
 */
const std::vector<int>& get_rearrange_indices() {
    std::call_once(g_indices_flag, []() {
        g_rearrange_indices.reserve(PULSE_GROUP_SIZE);
        for (int i = 0; i < 1024; i += 2)    g_rearrange_indices.push_back(i);
        for (int i = 1; i < 1024; i += 4)    g_rearrange_indices.push_back(i);
        for (int i = 3; i < 1024; i += 8)    g_rearrange_indices.push_back(i);
        for (int i = 7; i < 1024; i += 16)   g_rearrange_indices.push_back(i);
        for (int i = 15; i < 1024; i += 16)  g_rearrange_indices.push_back(i);
    });
    return g_rearrange_indices;
}

/**
 * 对单通道1024个脉冲数据进行重排并返回float数组（优化版本）
 */
std::vector<float> rearrange_channel_data(const std::vector<Pulse>& group) {
    const auto& indices = get_rearrange_indices();

    std::vector<float> data;
    data.reserve(PULSE_GROUP_SIZE * FLOAT_COUNT_PER_PULSE);

    // 按索引重排并汇聚到同一buffer - 使用更高效的内存拷贝
    for (int idx : indices) {
        const float* fp = reinterpret_cast<const float*>(group[idx].samples.data());
        data.insert(data.end(), fp, fp + FLOAT_COUNT_PER_PULSE);
    }
    return data;
}

/**
 * 对单通道1024个脉冲帧头进行重排并返回帧头数组（优化版本）
 */
std::vector<FrameHeader> rearrange_channel_headers(const std::vector<Pulse>& group) {
    const auto& indices = get_rearrange_indices();

    std::vector<FrameHeader> headers;
    headers.reserve(PULSE_GROUP_SIZE);

    // 按索引重排帧头
    for (int idx : indices) {
        headers.push_back(group[idx].header);
    }
    return headers;
}

/**
 * 合并并保存和、差通道的重排结果（优化版本）
 * 保存格式：和通道1024脉冲帧头 + 差通道1024脉冲帧头 + 和通道重排数据 + 差通道重排数据
 */
bool save_dual_channel(const std::vector<FrameHeader>& headers_sum,
                       const std::vector<float>& data_sum,
                       const std::vector<FrameHeader>& headers_diff,
                       const std::vector<float>& data_diff,
                       const std::string& out_dir,
                       const ProcessingOptions& options)
{
    try {
        // 创建目录
        fs::create_directories(out_dir);

        // 生成文件名（使用和通道第一个脉冲的信息作为基准）
        const auto& ref_header = headers_sum.front();
        std::string fname = std::to_string(ref_header.circle_num)
                          + "_" + std::to_string(ref_header.frame_num)
                          + "_" + std::to_string(ref_header.angle)
                          + ".bin";

        std::string out_path = out_dir + "/" + fname;

        std::ofstream out(out_path, std::ios::binary | std::ios::trunc);
        if (!out) return false;

        // 使用更大的缓冲区提高I/O效率
        auto buf = std::make_unique<char[]>(options.io_buffer_size);
        out.rdbuf()->pubsetbuf(buf.get(), options.io_buffer_size);

        // 计算总写入大小，一次性分配内存
        size_t total_size = 0;
        if (options.save_opts.save_sum_header) total_size += headers_sum.size() * HEADER_SIZE;
        if (options.save_opts.save_diff_header) total_size += headers_diff.size() * HEADER_SIZE;
        if (options.save_opts.save_sum_data) total_size += data_sum.size() * sizeof(float);
        if (options.save_opts.save_diff_data) total_size += data_diff.size() * sizeof(float);

        // 按选项写入数据 - 使用批量写入减少系统调用
        if (options.save_opts.save_sum_header) {
            out.write(reinterpret_cast<const char*>(headers_sum.data()),
                      headers_sum.size() * HEADER_SIZE);
        }

        if (options.save_opts.save_diff_header) {
            out.write(reinterpret_cast<const char*>(headers_diff.data()),
                      headers_diff.size() * HEADER_SIZE);
        }

        if (options.save_opts.save_sum_data) {
            out.write(reinterpret_cast<const char*>(data_sum.data()),
                      data_sum.size() * sizeof(float));
        }

        if (options.save_opts.save_diff_data) {
            out.write(reinterpret_cast<const char*>(data_diff.data()),
                      data_diff.size() * sizeof(float));
        }

        out.close();

        if (options.verbose_log) {
            std::cout << "已保存文件: " << out_path
                      << " (SH:" << options.save_opts.save_sum_header
                      << " DH:" << options.save_opts.save_diff_header
                      << " SD:" << options.save_opts.save_sum_data
                      << " DD:" << options.save_opts.save_diff_data << ")" << std::endl;
        }
        return true;
    } catch (...) {
        return false;
    }
}

/**
 * 处理单个 .bin 文件，基于数据格式：和通道帧头+和通道数据+差通道帧头+差通道数据
 * 优化的双通道匹配逻辑：利用相邻的和差通道数据必然具有相同脉冲ID的特性
 */
bool process_single_bin_file(const std::string& file_path,
                             int azimuth_min,
                             int azimuth_max,
                             const std::string& output_base,
                             const ProcessingOptions& options)
{
    auto t0 = std::chrono::steady_clock::now();
    if (options.verbose_log) {
        std::cout << "[开始] " << fs::path(file_path).filename() << std::endl;
    }

    std::ifstream in(file_path, std::ios::binary);
    if (!in) return false;
    char* in_buf = new char[options.io_buffer_size];
    in.rdbuf()->pubsetbuf(in_buf, options.io_buffer_size);

    // 输出目录 & CSV
    std::string stem = fs::path(file_path).stem().string();
    std::string dir_out = output_base + "/" + stem;
    fs::create_directories(dir_out);
    std::string bin_dir = dir_out + "/重排";
    std::string csv_path = dir_out + "/frames.csv";

    std::ofstream csv_out;
    std::ostringstream csv_buf;
    if (options.save_csv) {
        csv_out.open(csv_path);
        write_csv_header(csv_buf);
    }

    // 优化的双通道处理：利用数据格式特性
    std::deque<Pulse> buf_sum, buf_diff;  // 和通道、差通道缓冲区
    std::array<char, SAMPLE_DATA_SIZE> samp_buf;  // 使用固定大小数组
    FrameHeader header;
    size_t saved = 0;
    size_t total_sum = 0, total_diff = 0;  // 统计各通道脉冲数

    // 期待下一个帧的类型：1=和通道, 16=差通道
    uint16_t expected_channel = 1;  // 开始期待和通道

    while (in.read(reinterpret_cast<char*>(&header), HEADER_SIZE)) {
        if (header.marker != FRAME_HEADER_MARKER) {
            in.seekg(-static_cast<int>(HEADER_SIZE) + 1, std::ios::cur);
            continue;
        }
        if (!in.read(samp_buf.data(), SAMPLE_DATA_SIZE)) break;

        // CSV 记录
        if (options.save_csv) {
            append_csv_row(csv_buf, header);
        }

        // 方位角过滤
        if (header.angle < azimuth_min || header.angle > azimuth_max) {
            continue;
        }

        // 基于数据格式的优化处理逻辑
        if (header.channel == 1) {  // 和通道
            total_sum++;
            buf_sum.emplace_back(header, samp_buf.data());
            if (buf_sum.size() > MAX_PULSE_BUFFER_SIZE) buf_sum.pop_front();
            expected_channel = 16;  // 下一个应该是差通道

        } else if (header.channel == 16) {  // 差通道
            total_diff++;
            buf_diff.emplace_back(header, samp_buf.data());
            if (buf_diff.size() > MAX_PULSE_BUFFER_SIZE) buf_diff.pop_front();
            expected_channel = 1;   // 下一个应该是和通道
        }

        // // 定期输出缓冲区状态
        // if (options.verbose_log && (total_sum + total_diff) % 10000 == 0) {
        //     std::cout << "缓冲区状态 - 和通道: " << buf_sum.size()
        //               << ", 差通道: " << buf_diff.size()
        //               << ", 已保存组数: " << saved << std::endl;
        // }

        // 优化的脉冲组处理：利用数据格式的连续性
        // 当两个缓冲区都有足够数据时，尝试匹配和保存
        if (buf_sum.size() >= PULSE_GROUP_SIZE && buf_diff.size() >= PULSE_GROUP_SIZE) {
            // 查找和通道中的脉冲组起始位置（pulse_id==3）- 从后往前查找最新的
            auto sum_it = std::find_if(buf_sum.rbegin(), buf_sum.rend(),
                [](const auto& p){ return p.header.pulse_id == 3; });

            if (sum_it != buf_sum.rend()) {
                size_t sum_start = buf_sum.rend() - sum_it - 1;
                if (sum_start + PULSE_GROUP_SIZE <= buf_sum.size()) {
                    // 基于数据格式特性：查找对应的差通道脉冲组
                    const auto& ref_sum = buf_sum[sum_start];

                    // 优化的差通道匹配：使用更高效的查找策略
                    size_t diff_start = 0;
                    bool found_match = false;

                    // 首先在相近位置查找，利用数据的连续性
                    size_t search_start = (sum_start > 50) ? sum_start - 50 : 0;
                    size_t search_end = std::min(buf_diff.size() - PULSE_GROUP_SIZE + 1, sum_start + 100);

                    for (size_t i = search_start; i < search_end; i++) {
                        if (buf_diff[i].header.pulse_id == 3) {
                            const auto& ref_diff = buf_diff[i];

                            // 检查是否为同一脉冲组（相同的圈号、帧号、角度）
                            if (ref_sum.header.circle_num == ref_diff.header.circle_num &&
                                ref_sum.header.frame_num == ref_diff.header.frame_num &&
                                abs(static_cast<int>(ref_sum.header.angle) -
                                   static_cast<int>(ref_diff.header.angle)) <= 100) {
                                diff_start = i;
                                found_match = true;
                                break;
                            }
                        }
                    }

                    if (found_match) {
                        // 提取脉冲组 - 使用更高效的方式
                        std::vector<Pulse> grp_sum, grp_diff;
                        grp_sum.reserve(PULSE_GROUP_SIZE);
                        grp_diff.reserve(PULSE_GROUP_SIZE);

                        grp_sum.assign(buf_sum.begin() + sum_start,
                                      buf_sum.begin() + sum_start + PULSE_GROUP_SIZE);
                        grp_diff.assign(buf_diff.begin() + diff_start,
                                       buf_diff.begin() + diff_start + PULSE_GROUP_SIZE);

                        // 重排数据和帧头
                        auto data_sum = rearrange_channel_data(grp_sum);
                        auto data_diff = rearrange_channel_data(grp_diff);
                        auto headers_sum = rearrange_channel_headers(grp_sum);
                        auto headers_diff = rearrange_channel_headers(grp_diff);

                        // 保存双通道数据
                        if (save_dual_channel(
                            headers_sum, data_sum,
                            headers_diff, data_diff,
                            bin_dir, options)) {
                            saved++;
                        }

                        // 优化的缓冲区清理：保留更多数据以提高匹配效率
                        size_t keep_sum = PULSE_GROUP_SIZE / 2;  // 保留一半数据
                        size_t keep_diff = PULSE_GROUP_SIZE / 2;

                        if (buf_sum.size() > sum_start + keep_sum) {
                            buf_sum.erase(buf_sum.begin(), buf_sum.begin() + sum_start + keep_sum);
                        }
                        if (buf_diff.size() > diff_start + keep_diff) {
                            buf_diff.erase(buf_diff.begin(), buf_diff.begin() + diff_start + keep_diff);
                        }
                    }
                }
            }
        }
    }

    if (options.save_csv) {
        csv_out << csv_buf.str();
        csv_out.close();
    }
    in.close();
    delete[] in_buf;

    if (options.verbose_log) {
        auto t1 = std::chrono::steady_clock::now();
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(t1 - t0).count();
        std::cout << "[完成] " << fs::path(file_path).filename()
                  << "，和通道脉冲: " << total_sum
                  << "，差通道脉冲: " << total_diff
                  << "，保存组数: " << saved
                  << "，耗时: " << ms << " ms\n";
    }
    return true;
}

// 优化的多线程并行处理
void process_all_files_parallel(const std::string& input_dir,
                                const std::string& output_base,
                                int az_min, int az_max,
                                const ProcessingOptions& opts,
                                int threads = 0)  // 0表示自动检测
{
    if (!fs::exists(input_dir)) {
        std::cerr << "输入目录不存在: " << input_dir << std::endl;
        return;
    }
    fs::create_directories(output_base);

    // 收集 .bin 文件并按大小排序
    std::vector<std::pair<std::string, size_t>> file_sizes;
    for (const auto& entry : fs::directory_iterator(input_dir)) {
        if (entry.path().extension() == ".bin") {
            std::string filename = entry.path().filename().string();
            size_t underscore_pos = filename.find('_');
            bool valid = false;
        if (underscore_pos != std::string::npos) { // 必须存在至少一个下划线
            std::string first_part = filename.substr(0, underscore_pos);
            valid = (first_part == "data");
        }
        if (valid) {
            auto file_size = fs::file_size(entry.path());
            file_sizes.emplace_back(entry.path().string(), file_size);
        }
        }
    }

    if (file_sizes.empty()) {
        std::cout << "无 .bin 文件可处理\n";
        return;
    }

    // 按文件大小降序排序，大文件优先处理
    std::sort(file_sizes.begin(), file_sizes.end(),
              [](const auto& a, const auto& b) { return a.second > b.second; });

    // 优化线程数计算
    unsigned hw = std::thread::hardware_concurrency();
    if (threads <= 0) {
        threads = std::min(static_cast<int>(hw), MAX_THREADS);
    } else {
        threads = std::min({threads, MAX_THREADS, static_cast<int>(hw ? hw : 2)});
    }

    std::queue<std::string> file_queue;
    for (const auto& [file_path, _] : file_sizes) {
        file_queue.push(file_path);
    }

    std::mutex queue_mutex;
    std::atomic<size_t> completed{0};
    auto start_time = std::chrono::steady_clock::now();

    auto worker = [&](){
        while (true) {
            std::string file_path;
            {
                std::lock_guard<std::mutex> lock(queue_mutex);
                if (file_queue.empty()) break;
                file_path = file_queue.front();
                file_queue.pop();
            }

            process_single_bin_file(file_path, az_min, az_max, output_base, opts);

            size_t current_completed = ++completed;
            if (opts.verbose_log) {
                std::lock_guard<std::mutex> lock(queue_mutex);
                auto elapsed = std::chrono::steady_clock::now() - start_time;
                auto elapsed_ms = std::chrono::duration_cast<std::chrono::milliseconds>(elapsed).count();
                std::cout << "进度: " << current_completed << "/" << file_sizes.size()
                          << " (" << (100 * current_completed / file_sizes.size()) << "%)"
                          << " - " << fs::path(file_path).filename()
                          << " [" << elapsed_ms << "ms]\n";
            }
        }
    };

    std::vector<std::thread> workers;
    workers.reserve(threads);
    for (int i = 0; i < threads; i++) {
        workers.emplace_back(worker);
    }

    for (auto& worker_thread : workers) {
        if (worker_thread.joinable()) {
            worker_thread.join();
        }
    }

    auto total_time = std::chrono::steady_clock::now() - start_time;
    auto total_ms = std::chrono::duration_cast<std::chrono::milliseconds>(total_time).count();
    std::cout << "所有文件处理完毕，共 " << file_sizes.size() << " 个文件"
              << "，总耗时: " << total_ms << " ms"
              << "，使用线程数: " << threads << "\n";
}

// 打印使用说明
void print_usage() {
    std::cout << "双通道数据保存程序\n";
    std::cout << "可选保存组件：\n";
    std::cout << "  SH - 和通道1024个脉冲帧头 (40KB)\n";
    std::cout << "  DH - 差通道1024个脉冲帧头 (40KB)\n";
    std::cout << "  SD - 和通道重排数据 (~16MB)\n";
    std::cout << "  DD - 差通道重排数据 (~16MB)\n";
    std::cout << "\n修改main函数中的save_opts配置来选择保存的组件\n";
}

int main() {
    print_usage();

    std::string in_dir     = "/media/llh/新加卷/采集数据/0812(测试)";
    std::string out_base   = "/media/llh/新加卷/采集数据/0812(测试)/out";
    int az_min = 0 * 100, az_max = 360 * 100;

    ProcessingOptions opts;
    opts.save_csv     = true;
    opts.verbose_log  = true;
    opts.io_buffer_size = 65536;

    // 配置保存选项 - 可以根据需要修改
    opts.save_opts.save_sum_header = true;   // 保存和通道1024个脉冲帧头
    opts.save_opts.save_sum_data = true;     // 保存和通道重排数据
    opts.save_opts.save_diff_header = true;  // 保存差通道1024个脉冲帧头
    opts.save_opts.save_diff_data = true;    // 保存差通道重排数据

    // 修改打印顺序
    std::cout << "当前保存配置：\n";
    std::cout << "  和通道1024个脉冲帧头: " << (opts.save_opts.save_sum_header ? "是" : "否") << "\n";
    std::cout << "  差通道1024个脉冲帧头: " << (opts.save_opts.save_diff_header ? "是" : "否") << "\n";
    std::cout << "  和通道重排数据: " << (opts.save_opts.save_sum_data ? "是" : "否") << "\n";
    std::cout << "  差通道重排数据: " << (opts.save_opts.save_diff_data ? "是" : "否") << "\n\n";

    unsigned hw = std::thread::hardware_concurrency();
    int threads = hw? hw : 4;

    process_all_files_parallel(in_dir, out_base, az_min, az_max, opts, threads);
    return 0;
}
