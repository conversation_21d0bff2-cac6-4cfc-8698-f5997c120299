#include "unified_resource_manager.hpp"
#include <spdlog/spdlog.h>

namespace scr5000 {

// ==================== UnifiedResourceManager 实现 ====================

UnifiedResourceManager::UnifiedResourceManager()
    : group_start_frame_(-1), prev_azimuth_(INVALID_AZIMUTH),
      azimuth_unchanged_(false), initialized_(false) {}

UnifiedResourceManager::~UnifiedResourceManager() {
    cleanup();
}

void UnifiedResourceManager::cleanupUnlocked_() noexcept {
    try {
        current_group_detections_.clear();
        current_group_detections_.shrink_to_fit();
        group_start_frame_ = -1;
        prev_azimuth_ = INVALID_AZIMUTH;
        azimuth_unchanged_ = false;
        
        output_prob_.clear();
        output_prob_.shrink_to_fit();
        sample_input_.clear();
        sample_input_.shrink_to_fit();

        fft_optimizer_.reset();
        tracker_.reset();
        config_manager_.reset();
        memory_manager_.reset();
        gpu_manager_.reset();

        initialized_.store(false, std::memory_order_release);
    }
    catch (...) {
        spdlog::error("Unknown exception during cleanupUnlocked_");
    }
}

bool UnifiedResourceManager::initialize(const std::string& config_path) {
    std::lock_guard<std::mutex> lock(mutex_);

    if (initialized_.load(std::memory_order_acquire)) {
        spdlog::warn("Unified resource manager already initialized");
        return true;
    }

    try {
        spdlog::info("Initializing unified resource manager...");

        // 1) 先用临时对象完成全部构建
        auto cfg = std::make_unique<ConfigManager>(config_path);

        auto gpu = std::make_unique<GPUResourceManager>();
        if (!gpu->initialize()) {
            spdlog::error("Failed to initialize GPU resource manager");
            cleanupUnlocked_();
            return false;
        }

        auto mem = std::make_unique<MemoryPoolManager>();
        if (!mem->initialize()) {
            spdlog::error("Failed to initialize memory pool manager");
            cleanupUnlocked_();
            return false;
        }

        auto tracker = std::make_unique<PointTracker>(
            *cfg->get<int>("/algorithm_settings/tracker/max_age"),
            *cfg->get<int>("/algorithm_settings/tracker/reid_age"),
            *cfg->get<float>("/algorithm_settings/tracker/distance_threshold"),
            *cfg->get<int>("/algorithm_settings/tracker/min_hits")
        );

        auto fft = std::make_unique<FFTGPUOptimizer>(
            *cfg->get<int>("/algorithm_settings/fft/rows"),
            *cfg->get<int>("/algorithm_settings/fft/cols"),
            gpu.get()
        );

        // 2) 全部构建成功后，再赋值给成员变量
        config_manager_ = std::move(cfg);
        gpu_manager_ = std::move(gpu);
        memory_manager_ = std::move(mem);
        tracker_ = std::move(tracker);
        fft_optimizer_ = std::move(fft);

        initialized_.store(true, std::memory_order_release);
        spdlog::info("Unified resource manager initialized successfully");
        return true;
    } catch (const std::exception& e) {
        spdlog::error("Exception during unified resource manager initialization: {}", e.what());
        cleanupUnlocked_();
        return false;
    } catch (...) {
        spdlog::error("Unknown exception during unified resource manager initialization");
        cleanupUnlocked_();
        return false;
    }
}

void UnifiedResourceManager::cleanup() {
    std::lock_guard<std::mutex> lock(mutex_);
    spdlog::info("Cleaning up unified resource manager...");
    cleanupUnlocked_();
    spdlog::info("Unified resource manager cleaned up");
}

bool UnifiedResourceManager::isInitialized() const {
    return initialized_.load(std::memory_order_acquire);
}

// 资源访问器实现
GPUResourceManager* UnifiedResourceManager::getGPUManager() {
    std::lock_guard<std::mutex> lock(mutex_);
    return gpu_manager_.get();
}

MemoryPoolManager* UnifiedResourceManager::getMemoryManager() {
    std::lock_guard<std::mutex> lock(mutex_);
    return memory_manager_.get();
}

PointTracker* UnifiedResourceManager::getTracker() {
    std::lock_guard<std::mutex> lock(mutex_);
    return tracker_.get();
}

FFTGPUOptimizer* UnifiedResourceManager::getFFTOptimizer() {
    std::lock_guard<std::mutex> lock(mutex_);
    return fft_optimizer_.get();
}

ConfigManager* UnifiedResourceManager::getConfigManager() {
    std::lock_guard<std::mutex> lock(mutex_);
    return config_manager_.get();
}

// 缓冲区访问器实现
std::vector<float>& UnifiedResourceManager::getOutputProb() {
    return output_prob_;
}

std::vector<float>& UnifiedResourceManager::getSampleInput() {
    return sample_input_;
}

// 跟踪状态访问器实现
std::vector<Point>& UnifiedResourceManager::getCurrentGroupDetections() {
    return current_group_detections_;
}

int& UnifiedResourceManager::getGroupStartFrame() {
    return group_start_frame_;
}

float& UnifiedResourceManager::getPrevAzimuth() {
    return prev_azimuth_;
}

bool& UnifiedResourceManager::getAzimuthUnchanged() {
    return azimuth_unchanged_;
}

// 常量访问器实现
int UnifiedResourceManager::getFramesPerGroup() const {
    return FRAMES_PER_GROUP;
}

float UnifiedResourceManager::getInvalidAzimuth() const {
    return INVALID_AZIMUTH;
}

// ==================== 全局访问函数实现 ====================
static std::unique_ptr<UnifiedResourceManager> g_resource_manager = nullptr;
static std::mutex g_resource_mutex;

bool initializeResourceManager(const std::string& config_path) {
    std::lock_guard<std::mutex> lock(g_resource_mutex);

    if (g_resource_manager && g_resource_manager->isInitialized()) {
        spdlog::debug("Resource manager already initialized");
        return true;
    }

    try {
        g_resource_manager = std::make_unique<UnifiedResourceManager>();
        return g_resource_manager->initialize(config_path);
    } catch (const std::exception& e) {
        spdlog::error("Exception during resource manager initialization: {}", e.what());
        g_resource_manager.reset();
        return false;
    }
}

void cleanupResourceManager() {
    std::lock_guard<std::mutex> lock(g_resource_mutex);

    if (g_resource_manager) {
        g_resource_manager->cleanup();
        g_resource_manager.reset();
    }
}

UnifiedResourceManager* getResourceManager() {
    std::lock_guard<std::mutex> lock(g_resource_mutex);
    return g_resource_manager.get();
}

} // namespace scr5000