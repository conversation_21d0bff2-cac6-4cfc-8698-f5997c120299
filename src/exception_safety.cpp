#include "exception_safety.hpp"
#include <thread>

namespace scr5000 {

// ==================== 全局错误恢复管理器 ====================
static std::unique_ptr<ErrorRecoveryManager> g_recovery_manager = nullptr;
static std::mutex g_recovery_manager_mutex;

ErrorRecoveryManager* getGlobalRecoveryManager() {
    std::lock_guard<std::mutex> lock(g_recovery_manager_mutex);
    
    if (!g_recovery_manager) {
        g_recovery_manager = std::make_unique<ErrorRecoveryManager>();
        
        // 注册默认的恢复动作
        registerDefaultRecoveryActions();
    }
    
    return g_recovery_manager.get();
}

void releaseGlobalRecoveryManager() {
    std::lock_guard<std::mutex> lock(g_recovery_manager_mutex);
    g_recovery_manager.reset();
}

void registerDefaultRecoveryActions() {
    auto* manager = g_recovery_manager.get();
    if (!manager) return;
    
    // CUDA设备重置恢复动作
    manager->registerRecoveryAction(
        "CUDA Device Reset",
        []() -> bool {
            try {
                spdlog::info("Attempting CUDA device reset...");
                cudaError_t status = cudaDeviceReset();
                if (status == cudaSuccess) {
                    spdlog::info("CUDA device reset successful");
                    return true;
                } else {
                    spdlog::error("CUDA device reset failed: {}", cudaGetErrorString(status));
                    return false;
                }
            } catch (const std::exception& e) {
                spdlog::error("Exception during CUDA device reset: {}", e.what());
                return false;
            }
        },
        100, // 高优先级
        1,   // 只尝试一次
        std::chrono::milliseconds(2000)
    );
    
    // 内存清理恢复动作
    manager->registerRecoveryAction(
        "Memory Cleanup",
        []() -> bool {
            try {
                spdlog::info("Attempting memory cleanup...");
                
                // 触发垃圾回收（如果有的话）
                // 这里可以添加具体的内存清理逻辑
                
                // 检查GPU内存状态
                size_t free_mem = 0, total_mem = 0;
                cudaError_t status = cudaMemGetInfo(&free_mem, &total_mem);
                if (status == cudaSuccess) {
                    double usage = static_cast<double>(total_mem - free_mem) / total_mem * 100.0;
                    spdlog::info("GPU memory usage after cleanup: {:.1f}%", usage);
                    
                    // 如果内存使用率降到合理范围，认为恢复成功
                    return usage < 90.0;
                }
                
                return false;
                
            } catch (const std::exception& e) {
                spdlog::error("Exception during memory cleanup: {}", e.what());
                return false;
            }
        },
        80, // 中等优先级
        2,  // 尝试2次
        std::chrono::milliseconds(1000)
    );
    
    // 资源重新初始化恢复动作
    manager->registerRecoveryAction(
        "Resource Reinitialization",
        []() -> bool {
            try {
                spdlog::info("Attempting resource reinitialization...");
                
                // 这里可以添加重新初始化关键资源的逻辑
                // 例如重新创建CUDA上下文、重新加载模型等
                
                spdlog::info("Resource reinitialization completed");
                return true;
                
            } catch (const std::exception& e) {
                spdlog::error("Exception during resource reinitialization: {}", e.what());
                return false;
            }
        },
        60, // 较低优先级
        3,  // 尝试3次
        std::chrono::milliseconds(5000)
    );
    
    spdlog::info("Default recovery actions registered");
}

// ==================== 异常处理工具函数 ====================
void logException(const std::exception& e, const std::string& context) {
    if (const auto* scr_ex = dynamic_cast<const SCRException*>(&e)) {
        spdlog::error("SCR Exception [{}]: {}", context, scr_ex->getDetailedMessage());
    } else if (const auto* cuda_ex = dynamic_cast<const CudaException*>(&e)) {
        spdlog::error("CUDA Exception [{}]: {} (CUDA Error: {})", 
                     context, cuda_ex->what(), static_cast<int>(cuda_ex->getCudaError()));
    } else {
        spdlog::error("Standard Exception [{}]: {}", context, e.what());
    }
}

bool handleExceptionWithRecovery(const std::exception& e, const std::string& context) {
    logException(e, context);
    
    auto* manager = getGlobalRecoveryManager();
    if (manager && !manager->isRecoveryInProgress()) {
        spdlog::info("Attempting automatic error recovery...");
        return manager->attemptRecovery(context);
    } else {
        spdlog::warn("Recovery manager not available or recovery already in progress");
        return false;
    }
}

// ==================== 异常安全的CUDA操作包装器 ====================
bool safeCudaMalloc(void** ptr, size_t size, const std::string& context) {
    try {
        CUDA_CHECK_THROW(cudaMalloc(ptr, size));
        spdlog::debug("CUDA malloc successful: {} bytes in {}", size, context);
        return true;
        
    } catch (const CudaException& e) {
        spdlog::error("CUDA malloc failed in {}: {}", context, e.what());
        
        // 尝试恢复
        if (handleExceptionWithRecovery(e, context + " - CUDA malloc")) {
            // 恢复成功，重试一次
            try {
                CUDA_CHECK_THROW(cudaMalloc(ptr, size));
                spdlog::info("CUDA malloc succeeded after recovery in {}", context);
                return true;
            } catch (const CudaException& retry_e) {
                spdlog::error("CUDA malloc failed again after recovery in {}: {}", context, retry_e.what());
            }
        }
        
        return false;
    }
}

bool safeCudaFree(void* ptr, const std::string& context) {
    if (!ptr) return true;
    
    try {
        CUDA_CHECK_THROW(cudaFree(ptr));
        spdlog::debug("CUDA free successful in {}", context);
        return true;
        
    } catch (const CudaException& e) {
        spdlog::error("CUDA free failed in {}: {}", context, e.what());
        // 对于释放操作，通常不进行恢复尝试
        return false;
    }
}

bool safeCudaMemcpy(void* dst, const void* src, size_t count, cudaMemcpyKind kind, const std::string& context) {
    try {
        CUDA_CHECK_THROW(cudaMemcpy(dst, src, count, kind));
        spdlog::debug("CUDA memcpy successful: {} bytes in {}", count, context);
        return true;
        
    } catch (const CudaException& e) {
        spdlog::error("CUDA memcpy failed in {}: {}", context, e.what());
        
        // 尝试恢复
        if (handleExceptionWithRecovery(e, context + " - CUDA memcpy")) {
            // 恢复成功，重试一次
            try {
                CUDA_CHECK_THROW(cudaMemcpy(dst, src, count, kind));
                spdlog::info("CUDA memcpy succeeded after recovery in {}", context);
                return true;
            } catch (const CudaException& retry_e) {
                spdlog::error("CUDA memcpy failed again after recovery in {}: {}", context, retry_e.what());
            }
        }
        
        return false;
    }
}

// ==================== 异常安全的资源管理辅助函数 ====================
template<typename T>
std::unique_ptr<T, std::function<void(T*)>> makeUniqueWithDeleter(T* ptr, std::function<void(T*)> deleter) {
    return std::unique_ptr<T, std::function<void(T*)>>(ptr, deleter);
}

// 创建异常安全的CUDA内存
std::unique_ptr<void, std::function<void(void*)>> makeSafeCudaMemory(size_t size, const std::string& context) {
    void* ptr = nullptr;

    if (!safeCudaMalloc(&ptr, size, context)) {
        throw MemoryException("Failed to allocate CUDA memory", size, context);
    }

    // 直接创建unique_ptr，不使用模板函数
    return std::unique_ptr<void, std::function<void(void*)>>(ptr, [context](void* p) {
        if (p) {
            safeCudaFree(p, context + " - cleanup");
        }
    });
}

// ==================== 异常安全的初始化序列 ====================
class SafeInitializationSequence {
private:
    std::vector<std::function<void()>> cleanup_actions_;
    bool committed_;
    std::string sequence_name_;

public:
    explicit SafeInitializationSequence(const std::string& name) 
        : committed_(false), sequence_name_(name) {
        spdlog::debug("Starting safe initialization sequence: {}", name);
    }

    ~SafeInitializationSequence() {
        if (!committed_) {
            spdlog::warn("Initialization sequence '{}' not committed, performing cleanup", sequence_name_);
            cleanup();
        }
    }

    template<typename InitFunc, typename CleanupFunc>
    void addStep(const std::string& step_name, InitFunc init, CleanupFunc cleanup) {
        try {
            spdlog::debug("Executing initialization step: {} in {}", step_name, sequence_name_);
            init();
            cleanup_actions_.push_back([cleanup, step_name, this]() {
                try {
                    spdlog::debug("Cleaning up step: {} in {}", step_name, sequence_name_);
                    cleanup();
                } catch (const std::exception& e) {
                    spdlog::error("Exception during cleanup of step '{}' in '{}': {}", 
                                 step_name, sequence_name_, e.what());
                }
            });
            spdlog::debug("Initialization step '{}' completed successfully", step_name);
            
        } catch (const std::exception& e) {
            spdlog::error("Initialization step '{}' failed in '{}': {}", step_name, sequence_name_, e.what());
            cleanup();
            throw;
        }
    }

    void commit() {
        committed_ = true;
        cleanup_actions_.clear();
        spdlog::info("Initialization sequence '{}' committed successfully", sequence_name_);
    }

private:
    void cleanup() {
        for (auto it = cleanup_actions_.rbegin(); it != cleanup_actions_.rend(); ++it) {
            (*it)();
        }
        cleanup_actions_.clear();
    }
};

// ==================== 全局异常处理器 ====================
void setupGlobalExceptionHandler() {
    std::set_terminate([]() {
        try {
            auto eptr = std::current_exception();
            if (eptr) {
                std::rethrow_exception(eptr);
            } else {
                spdlog::critical("Terminate called without an active exception");
            }
        } catch (const SCRException& e) {
            spdlog::critical("Unhandled SCR Exception: {}", e.getDetailedMessage());
        } catch (const std::exception& e) {
            spdlog::critical("Unhandled standard exception: {}", e.what());
        } catch (...) {
            spdlog::critical("Unhandled unknown exception");
        }
        
        // 尝试最后的清理
        try {
            releaseGlobalRecoveryManager();
        } catch (...) {
            // 忽略清理时的异常
        }
        
        std::abort();
    });
    
    spdlog::info("Global exception handler setup completed");
}

} // namespace scr5000
