#include "debug_tools.hpp"
#include <iomanip>
#include <limits>

namespace scr5000 {

// ==================== 静态成员定义 ====================
std::unique_ptr<DebugToolsManager> DebugToolsManager::instance_ = nullptr;
std::mutex DebugToolsManager::instance_mutex_;

// ==================== 增强的内存监控功能 ====================
void MemoryMonitor::logDetailedMemoryInfo() {
    // GPU内存信息
    size_t free_mem = 0, total_mem = 0;
    cudaError_t status = cudaMemGetInfo(&free_mem, &total_mem);
    
    if (status == cudaSuccess) {
        size_t used_mem = total_mem - free_mem;
        double usage_percent = static_cast<double>(used_mem) / total_mem * 100.0;
        
        spdlog::info("=== Detailed GPU Memory Info ===");
        spdlog::info("  Total: {:.1f} MB", total_mem / (1024.0 * 1024.0));
        spdlog::info("  Used:  {:.1f} MB ({:.1f}%)", used_mem / (1024.0 * 1024.0), usage_percent);
        spdlog::info("  Free:  {:.1f} MB ({:.1f}%)", free_mem / (1024.0 * 1024.0), 100.0 - usage_percent);
        
        // 检查内存使用警告
        if (usage_percent > 90.0) {
            spdlog::warn("GPU memory usage is critically high: {:.1f}%", usage_percent);
        } else if (usage_percent > 75.0) {
            spdlog::warn("GPU memory usage is high: {:.1f}%", usage_percent);
        }
    } else {
        spdlog::error("Failed to get GPU memory info: {}", cudaGetErrorString(status));
    }
    
    // CUDA设备信息
    int device_count = 0;
    status = cudaGetDeviceCount(&device_count);
    if (status == cudaSuccess) {
        spdlog::info("=== CUDA Device Info ===");
        spdlog::info("  Device count: {}", device_count);
        
        for (int i = 0; i < device_count; ++i) {
            cudaDeviceProp prop;
            status = cudaGetDeviceProperties(&prop, i);
            if (status == cudaSuccess) {
                spdlog::info("  Device {}: {} (Compute {}.{})", 
                           i, prop.name, prop.major, prop.minor);
                spdlog::info("    Global memory: {:.1f} MB", 
                           prop.totalGlobalMem / (1024.0 * 1024.0));
                spdlog::info("    Multiprocessors: {}", prop.multiProcessorCount);
                spdlog::info("    Max threads per block: {}", prop.maxThreadsPerBlock);
            }
        }
    }
}

std::string MemoryMonitor::getMemoryTrend() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (snapshots_.size() < 2) {
        return "Insufficient data for trend analysis";
    }
    
    // 计算最近几个快照的趋势
    size_t recent_count = std::min(snapshots_.size(), size_t(10));
    size_t start_idx = snapshots_.size() - recent_count;
    
    double total_change = 0.0;
    for (size_t i = start_idx + 1; i < snapshots_.size(); ++i) {
        double prev_usage = static_cast<double>(snapshots_[i-1].gpu_memory_used);
        double curr_usage = static_cast<double>(snapshots_[i].gpu_memory_used);
        total_change += (curr_usage - prev_usage);
    }
    
    double avg_change_mb = total_change / (1024.0 * 1024.0) / (recent_count - 1);
    
    std::ostringstream oss;
    oss << "Memory trend (last " << recent_count << " snapshots): ";
    if (std::abs(avg_change_mb) < 0.1) {
        oss << "Stable";
    } else if (avg_change_mb > 0) {
        oss << "Increasing by " << std::fixed << std::setprecision(2) << avg_change_mb << " MB/snapshot";
    } else {
        oss << "Decreasing by " << std::fixed << std::setprecision(2) << -avg_change_mb << " MB/snapshot";
    }
    
    return oss.str();
}

// ==================== 增强的性能分析功能 ====================
void PerformanceProfiler::logTopPerformers(size_t top_n) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (profiles_.empty()) {
        spdlog::info("No profiling data available");
        return;
    }
    
    // 按总时间排序
    std::vector<std::pair<std::string, ProfileData>> sorted_profiles;
    for (const auto& [name, profile] : profiles_) {
        sorted_profiles.emplace_back(name, profile);
    }
    
    std::sort(sorted_profiles.begin(), sorted_profiles.end(),
              [](const auto& a, const auto& b) {
                  return a.second.total_time_ms > b.second.total_time_ms;
              });
    
    spdlog::info("=== Top {} Performance Consumers ===", std::min(top_n, sorted_profiles.size()));
    for (size_t i = 0; i < std::min(top_n, sorted_profiles.size()); ++i) {
        const auto& [name, profile] = sorted_profiles[i];
        spdlog::info("  {}: {:.3f}ms total ({:.3f}ms avg, {} calls)",
                    name, profile.total_time_ms, profile.getAverageTime(), profile.call_count);
    }
}

double PerformanceProfiler::getTotalTime() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    double total = 0.0;
    for (const auto& [name, profile] : profiles_) {
        total += profile.total_time_ms;
    }
    return total;
}

size_t PerformanceProfiler::getTotalCalls() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    size_t total = 0;
    for (const auto& [name, profile] : profiles_) {
        total += profile.call_count;
    }
    return total;
}

// ==================== 系统诊断功能 ====================
class SystemDiagnostics {
public:
    static void runFullDiagnostics() {
        spdlog::info("=== Running Full System Diagnostics ===");
        
        // CUDA诊断
        runCudaDiagnostics();
        
        // 内存诊断
        runMemoryDiagnostics();
        
        // 性能诊断
        runPerformanceDiagnostics();
        
        spdlog::info("=== System Diagnostics Complete ===");
    }
    
private:
    static void runCudaDiagnostics() {
        spdlog::info("--- CUDA Diagnostics ---");
        
        // 检查CUDA运行时版本
        int runtime_version = 0;
        cudaError_t status = cudaRuntimeGetVersion(&runtime_version);
        if (status == cudaSuccess) {
            spdlog::info("CUDA Runtime Version: {}.{}", 
                        runtime_version / 1000, (runtime_version % 100) / 10);
        }
        
        // 检查驱动版本
        int driver_version = 0;
        status = cudaDriverGetVersion(&driver_version);
        if (status == cudaSuccess) {
            spdlog::info("CUDA Driver Version: {}.{}", 
                        driver_version / 1000, (driver_version % 100) / 10);
        }
        
        // 检查设备状态
        int device = 0;
        status = cudaGetDevice(&device);
        if (status == cudaSuccess) {
            spdlog::info("Current CUDA Device: {}", device);
            
            // 测试简单的CUDA操作
            float* d_test = nullptr;
            status = cudaMalloc(&d_test, sizeof(float));
            if (status == cudaSuccess) {
                cudaFree(d_test);
                spdlog::info("CUDA memory allocation test: PASSED");
            } else {
                spdlog::error("CUDA memory allocation test: FAILED - {}", cudaGetErrorString(status));
            }
        }
    }
    
    static void runMemoryDiagnostics() {
        spdlog::info("--- Memory Diagnostics ---");
        
        auto* monitor = DebugToolsManager::getInstance()->getMemoryMonitor();
        if (monitor) {
            monitor->logDetailedMemoryInfo();
            spdlog::info(monitor->getMemoryTrend());
            spdlog::info("Memory snapshots collected: {}", monitor->getSnapshotCount());
        }
    }
    
    static void runPerformanceDiagnostics() {
        spdlog::info("--- Performance Diagnostics ---");
        
        auto* profiler = DebugToolsManager::getInstance()->getProfiler();
        if (profiler) {
            profiler->logTopPerformers(5);
            spdlog::info("Total profiled time: {:.3f}ms", profiler->getTotalTime());
            spdlog::info("Total profiled calls: {}", profiler->getTotalCalls());
        }
    }
};

// ==================== 调试工具管理器增强功能 ====================
void DebugToolsManager::runDiagnostics() {
    SystemDiagnostics::runFullDiagnostics();
}

void DebugToolsManager::enableAllMonitoring() {
    memory_monitor_->enable();
    profiler_->enable();
    spdlog::info("All debug monitoring enabled");
}

void DebugToolsManager::disableAllMonitoring() {
    memory_monitor_->disable();
    profiler_->disable();
    spdlog::info("All debug monitoring disabled");
}

void DebugToolsManager::clearAllData() {
    memory_monitor_->clear();
    profiler_->clear();
    
    std::lock_guard<std::mutex> lock(mutex_);
    timers_.clear();
    
    spdlog::info("All debug data cleared");
}

std::string DebugToolsManager::getStatusSummary() {
    std::ostringstream oss;
    oss << "Debug Tools Status:\n";
    oss << "  Memory Monitor: " << (memory_monitor_->isEnabled() ? "Enabled" : "Disabled") << "\n";
    oss << "  Performance Profiler: " << (profiler_->isEnabled() ? "Enabled" : "Disabled") << "\n";
    oss << "  Memory Snapshots: " << memory_monitor_->getSnapshotCount() << "\n";
    oss << "  Total Profiled Time: " << std::fixed << std::setprecision(3) << profiler_->getTotalTime() << "ms\n";
    
    std::lock_guard<std::mutex> lock(mutex_);
    oss << "  Active Timers: " << timers_.size() << "\n";
    
    return oss.str();
}

} // namespace scr5000
