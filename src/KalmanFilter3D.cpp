#include "KalmanFilter3D.hpp"

int KalmanFilter3D::next_id = 0;

KalmanFilter3D::KalmanFilter3D(const Eigen::Vector3f& init_pos, const Eigen::Vector3f& init_vel)
    : age(0), time_since_update(0), hit_streak(1), id(-1), id_assigned(false)
{
    // 调整时间步长以适应逐帧处理
    // 假设雷达扫描频率为20Hz，每帧间隔0.05秒
    float dt = 0.05f;

    // 状态转移矩阵 F (6x6): [位置; 速度] -> [位置; 速度]
    F.setIdentity();
    for (int i = 0; i < 3; ++i)
        F(i, i + 3) = dt;

    // 观测矩阵 H (6x6): 观测位置和速度
    H = Eigen::Matrix<float, 6, 6>::Identity();

    // 过程噪声协方差矩阵 Q (6x6)
    // 调整以适应更短的时间间隔和扫描雷达特性
    Q = Eigen::Matrix<float, 6, 6>::Identity();
    // 位置过程噪声 (考虑目标机动性)
    Q.block<3,3>(0,0) *= 1.0f;
    // 速度过程噪声 (考虑加速度变化)
    Q.block<3,3>(3,3) *= 5.0f;

    // 观测噪声协方差矩阵 R (3x3) - 仅位置观测
    R = Eigen::Matrix3f::Identity() * 10.0f;  // 雷达测量噪声约10米

    // 初始状态协方差矩阵 P (6x6)
    P = Eigen::Matrix<float, 6, 6>::Identity();
    P.block<3,3>(0,0) *= 100.0f;  // 初始位置不确定性
    P.block<3,3>(3,3) *= 50.0f;   // 初始速度不确定性

    x.setZero();
    x.block<3,1>(0,0) = init_pos;
    x.block<3,1>(3,0) = init_vel;

    history.clear();
    history.push_back(init_pos);
}

void KalmanFilter3D::predict() {
    // 状态预测
    x = F * x;

    // 协方差预测，考虑多帧无观测时的不确定性增长
    P = F * P * F.transpose() + Q;

    // 对于长时间无观测的目标，增加额外的不确定性
    if (time_since_update > 10) {  // 超过10帧无观测
        float uncertainty_factor = 1.0f + 0.1f * (time_since_update - 10);
        uncertainty_factor = std::min(uncertainty_factor, 3.0f);  // 限制最大增长

        // 增加位置和速度的不确定性
        P.block<3,3>(0,0) *= uncertainty_factor;
        P.block<3,3>(3,3) *= uncertainty_factor;
    }

    history.push_back(x.block<3, 1>(0, 0));
    age++;
    time_since_update++;
}

void KalmanFilter3D::update(const Eigen::Vector3f& z_pos, const Eigen::Vector3f& z_vel) {
    Eigen::Matrix<float, 6, 1> z;
    z.block<3,1>(0,0) = z_pos;
    z.block<3,1>(3,0) = z_vel;

    Eigen::Matrix<float, 6, 6> H_full = Eigen::Matrix<float, 6, 6>::Zero();
    H_full.block<3,3>(0,0) = Eigen::Matrix3f::Identity();
    H_full.block<3,3>(3,3) = Eigen::Matrix3f::Identity();

    Eigen::Matrix<float, 6, 6> R_full = Eigen::Matrix<float, 6, 6>::Identity() * 0.1f;

    Eigen::Matrix<float, 6, 1> y = z - H_full * x;
    Eigen::Matrix<float, 6, 6> S = H_full * P * H_full.transpose() + R_full;
    Eigen::Matrix<float, 6, 6> K = P * H_full.transpose() * S.inverse();
    x = x + K * y;
    P = (Eigen::Matrix<float, 6, 6>::Identity() - K * H_full) * P;

    history.push_back(x.block<3, 1>(0, 0));
    time_since_update = 0;
    hit_streak++;
}

void KalmanFilter3D::reset(const Eigen::Vector3f& new_pos, const Eigen::Vector3f& new_vel) {
    x.setZero();
    x.block<3,1>(0,0) = new_pos;
    x.block<3,1>(3,0) = new_vel;

    // 重置状态协方差矩阵，适应重新识别的不确定性
    P = Eigen::Matrix<float, 6, 6>::Identity();
    P.block<3,3>(0,0) *= 200.0f;  // 重识别时位置不确定性较大
    P.block<3,3>(3,3) *= 100.0f;  // 重识别时速度不确定性较大

    history.clear();
    history.push_back(new_pos);

    age = 1;
    time_since_update = 0;
    hit_streak = 2;
}

Eigen::Vector3f KalmanFilter3D::getPrediction() const {
    return x.block<3,1>(0,0);
}

Eigen::Vector3f KalmanFilter3D::getVelocity() const {
    return x.block<3,1>(3,0);
}

int KalmanFilter3D::getId() const { return id; }
int KalmanFilter3D::getAge() const { return age; }
int KalmanFilter3D::getTimeSinceUpdate() const { return time_since_update; }
int KalmanFilter3D::getHitStreak() const { return hit_streak; }
bool KalmanFilter3D::hasId() const { return id_assigned; }
void KalmanFilter3D::assignId() {
    if (!id_assigned) {
        id = next_id;
        next_id = (next_id + 1) % 9999;
        id_assigned = true;
    }
}

std::vector<Eigen::Vector3f> KalmanFilter3D::getHistory() const {
    return history;
}

// 新增高频预测输出(仅预测位置)
Eigen::Vector3f KalmanFilter3D::predictAt(float dt) const
{
    // 临时构造一个与当前 F 相同结构的状态转移矩阵，但 dt 可变
    Eigen::Matrix<float, 6, 6> F_tmp = Eigen::Matrix<float, 6, 6>::Identity();
    for (int i = 0; i < 3; ++i) F_tmp(i, i + 3) = dt;

    Eigen::Vector<float, 6> x_tmp = F_tmp * x;
    return x_tmp.block<3, 1>(0, 0);
}