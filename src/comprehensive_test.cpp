#include "comprehensive_test.hpp"
#include "libSCR_5000_Alg.hpp"
#include "resource_manager.hpp"
#include "memory_pool.hpp"
#include "unified_resource_manager.hpp"
#include "debug_tools.hpp"
#include "exception_safety.hpp"
#include "performance_optimizer.hpp"
#include <random>
#include <thread>

namespace scr5000 {

// ==================== 具体测试实现 ====================

// 基础功能测试
TestResult testBasicInitialization() {
    try {
        // 测试库初始化
        int result = InitializeAlgorithmLibrary("config/test_config.json");
        if (result != 0) {
            spdlog::error("Algorithm library initialization failed");
            return TestResult::FAILED;
        }
        
        // 清理
        ReleaseAllResources();
        
        spdlog::info("Basic initialization test passed");
        return TestResult::PASSED;
        
    } catch (const std::exception& e) {
        spdlog::error("Exception in basic initialization test: {}", e.what());
        return TestResult::ERROR;
    }
}

TestResult testMemoryAllocation() {
    try {
        // 测试CUDA内存分配
        void* cuda_ptr = nullptr;
        cudaError_t status = cudaMalloc(&cuda_ptr, 1024 * 1024); // 1MB
        
        if (status != cudaSuccess) {
            spdlog::error("CUDA memory allocation failed: {}", cudaGetErrorString(status));
            return TestResult::FAILED;
        }
        
        // 测试内存访问
        status = cudaMemset(cuda_ptr, 0, 1024 * 1024);
        if (status != cudaSuccess) {
            cudaFree(cuda_ptr);
            spdlog::error("CUDA memory access failed: {}", cudaGetErrorString(status));
            return TestResult::FAILED;
        }
        
        // 清理
        cudaFree(cuda_ptr);
        
        spdlog::info("Memory allocation test passed");
        return TestResult::PASSED;
        
    } catch (const std::exception& e) {
        spdlog::error("Exception in memory allocation test: {}", e.what());
        return TestResult::ERROR;
    }
}

TestResult testResourceManager() {
    try {
        // 测试GPU资源管理器
        auto* gpu_manager = getGPUResourceManager();
        if (!gpu_manager) {
            spdlog::error("GPU resource manager not available");
            return TestResult::FAILED;
        }
        
        // 测试初始化
        if (!gpu_manager->initialize()) {
            spdlog::error("GPU resource manager initialization failed");
            return TestResult::FAILED;
        }
        
        // 测试内存池管理器（通过UnifiedResourceManager）
        auto* unified_manager = scr5000::getResourceManager();
        if (!unified_manager) {
            spdlog::error("Unified resource manager not available");
            return TestResult::FAILED;
        }

        auto* memory_manager = unified_manager->getMemoryManager();
        if (!memory_manager) {
            spdlog::error("Memory pool manager not available");
            return TestResult::FAILED;
        }
        
        spdlog::info("Resource manager test passed");
        return TestResult::PASSED;
        
    } catch (const std::exception& e) {
        spdlog::error("Exception in resource manager test: {}", e.what());
        return TestResult::ERROR;
    }
}

TestResult testPerformanceOptimization() {
    try {
        // 测试性能优化系统
        if (!initializePerformanceOptimization()) {
            spdlog::error("Performance optimization initialization failed");
            return TestResult::FAILED;
        }
        
        // 测试内存分配
        float* cuda_float = allocateOptimizedCudaFloat(1024);
        if (!cuda_float) {
            spdlog::error("Optimized CUDA float allocation failed");
            return TestResult::FAILED;
        }
        
        int* host_int = allocateOptimizedHostInt(512);
        if (!host_int) {
            deallocateOptimizedCudaFloat(cuda_float);
            spdlog::error("Optimized host int allocation failed");
            return TestResult::FAILED;
        }
        
        // 清理
        deallocateOptimizedCudaFloat(cuda_float);
        deallocateOptimizedHostInt(host_int);
        
        spdlog::info("Performance optimization test passed");
        return TestResult::PASSED;
        
    } catch (const std::exception& e) {
        spdlog::error("Exception in performance optimization test: {}", e.what());
        return TestResult::ERROR;
    }
}

TestResult testExceptionSafety() {
    try {
        // 测试异常安全机制
        setupGlobalExceptionHandler();
        
        auto* recovery_manager = getGlobalRecoveryManager();
        if (!recovery_manager) {
            spdlog::error("Global recovery manager not available");
            return TestResult::FAILED;
        }
        
        // 测试安全的CUDA操作
        void* ptr = nullptr;
        if (!safeCudaMalloc(&ptr, 1024, "test_context")) {
            spdlog::error("Safe CUDA malloc failed");
            return TestResult::FAILED;
        }
        
        if (!safeCudaFree(ptr, "test_context")) {
            spdlog::warn("Safe CUDA free failed (non-critical)");
        }
        
        spdlog::info("Exception safety test passed");
        return TestResult::PASSED;
        
    } catch (const std::exception& e) {
        spdlog::error("Exception in exception safety test: {}", e.what());
        return TestResult::ERROR;
    }
}

TestResult testDebugTools() {
    try {
        // 测试调试工具
        auto* debug_manager = DebugToolsManager::getInstance();
        if (!debug_manager) {
            spdlog::error("Debug tools manager not available");
            return TestResult::FAILED;
        }
        
        // 测试内存监控
        auto* memory_monitor = debug_manager->getMemoryMonitor();
        if (!memory_monitor) {
            spdlog::error("Memory monitor not available");
            return TestResult::FAILED;
        }
        
        memory_monitor->takeSnapshot("test_snapshot");
        
        // 测试性能分析器
        auto* profiler = debug_manager->getProfiler();
        if (!profiler) {
            spdlog::error("Performance profiler not available");
            return TestResult::FAILED;
        }
        
        profiler->recordTime("test_operation", 10.5);
        
        spdlog::info("Debug tools test passed");
        return TestResult::PASSED;
        
    } catch (const std::exception& e) {
        spdlog::error("Exception in debug tools test: {}", e.what());
        return TestResult::ERROR;
    }
}

TestResult testStressTest() {
    try {
        spdlog::info("Starting stress test...");
        
        const int num_iterations = 100;
        const int allocation_size = 1024 * 1024; // 1MB
        
        std::vector<void*> allocations;
        allocations.reserve(num_iterations);
        
        // 分配阶段
        for (int i = 0; i < num_iterations; ++i) {
            void* ptr = nullptr;
            cudaError_t status = cudaMalloc(&ptr, allocation_size);
            
            if (status != cudaSuccess) {
                // 清理已分配的内存
                for (void* allocated_ptr : allocations) {
                    cudaFree(allocated_ptr);
                }
                spdlog::error("Stress test allocation failed at iteration {}: {}", 
                             i, cudaGetErrorString(status));
                return TestResult::FAILED;
            }
            
            allocations.push_back(ptr);
            
            // 每10次迭代记录一次进度
            if ((i + 1) % 10 == 0) {
                spdlog::debug("Stress test progress: {}/{} allocations", i + 1, num_iterations);
            }
        }
        
        // 释放阶段
        for (void* ptr : allocations) {
            cudaFree(ptr);
        }
        
        spdlog::info("Stress test passed: {} allocations/deallocations completed", num_iterations);
        return TestResult::PASSED;
        
    } catch (const std::exception& e) {
        spdlog::error("Exception in stress test: {}", e.what());
        return TestResult::ERROR;
    }
}

TestResult testConcurrency() {
    try {
        spdlog::info("Starting concurrency test...");
        
        const int num_threads = 4;
        const int operations_per_thread = 50;
        std::atomic<int> success_count{0};
        std::atomic<int> failure_count{0};
        
        std::vector<std::thread> threads;
        
        for (int t = 0; t < num_threads; ++t) {
            threads.emplace_back([&, t]() {
                for (int i = 0; i < operations_per_thread; ++i) {
                    try {
                        // 测试并发内存分配
                        void* ptr = nullptr;
                        cudaError_t status = cudaMalloc(&ptr, 1024);
                        
                        if (status == cudaSuccess) {
                            // 简单的内存操作
                            cudaMemset(ptr, t, 1024);
                            cudaFree(ptr);
                            success_count.fetch_add(1);
                        } else {
                            failure_count.fetch_add(1);
                        }
                        
                        // 短暂休眠以增加并发性
                        std::this_thread::sleep_for(std::chrono::milliseconds(1));
                        
                    } catch (const std::exception& e) {
                        spdlog::error("Exception in thread {}: {}", t, e.what());
                        failure_count.fetch_add(1);
                    }
                }
            });
        }
        
        // 等待所有线程完成
        for (auto& thread : threads) {
            thread.join();
        }
        
        int total_operations = num_threads * operations_per_thread;
        int actual_operations = success_count.load() + failure_count.load();
        
        spdlog::info("Concurrency test completed: {}/{} operations successful, {} failed",
                    success_count.load(), actual_operations, failure_count.load());
        
        // 如果成功率超过90%，认为测试通过
        double success_rate = static_cast<double>(success_count.load()) / actual_operations * 100.0;
        if (success_rate >= 90.0) {
            spdlog::info("Concurrency test passed with {:.1f}% success rate", success_rate);
            return TestResult::PASSED;
        } else {
            spdlog::error("Concurrency test failed with {:.1f}% success rate", success_rate);
            return TestResult::FAILED;
        }
        
    } catch (const std::exception& e) {
        spdlog::error("Exception in concurrency test: {}", e.what());
        return TestResult::ERROR;
    }
}

// ==================== 测试套件创建函数 ====================
std::unique_ptr<TestSuite> createBasicTestSuite() {
    auto suite = std::make_unique<TestSuite>("Basic Functionality Tests");
    
    suite->addTestCase("Basic Initialization", 
                      "Test basic library initialization and cleanup",
                      testBasicInitialization);
    
    suite->addTestCase("Memory Allocation", 
                      "Test basic CUDA memory allocation and access",
                      testMemoryAllocation);
    
    suite->addTestCase("Resource Manager", 
                      "Test resource manager initialization and functionality",
                      testResourceManager);
    
    return suite;
}

std::unique_ptr<TestSuite> createAdvancedTestSuite() {
    auto suite = std::make_unique<TestSuite>("Advanced Features Tests");
    
    suite->addTestCase("Performance Optimization", 
                      "Test performance optimization system",
                      testPerformanceOptimization);
    
    suite->addTestCase("Exception Safety", 
                      "Test exception safety mechanisms",
                      testExceptionSafety);
    
    suite->addTestCase("Debug Tools", 
                      "Test debug and monitoring tools",
                      testDebugTools);
    
    return suite;
}

std::unique_ptr<TestSuite> createStressTestSuite() {
    auto suite = std::make_unique<TestSuite>("Stress and Performance Tests");
    
    suite->addTestCase("Stress Test", 
                      "Test system under heavy memory allocation load",
                      testStressTest,
                      std::chrono::milliseconds(60000)); // 60秒超时
    
    suite->addTestCase("Concurrency Test", 
                      "Test system under concurrent access",
                      testConcurrency,
                      std::chrono::milliseconds(30000)); // 30秒超时
    
    return suite;
}

// ==================== 主测试运行函数 ====================
int runComprehensiveTests(const std::string& output_dir) {
    try {
        spdlog::info("Starting SCR-5000 comprehensive test suite");
        
        TestRunner runner(output_dir, true);
        runner.setStopOnFailure(false); // 继续执行所有测试
        
        // 添加测试套件
        runner.addTestSuite(createBasicTestSuite());
        runner.addTestSuite(createAdvancedTestSuite());
        runner.addTestSuite(createStressTestSuite());
        
        // 运行所有测试
        auto result = runner.runAllTests();
        
        // 输出最终结果
        spdlog::info("=== Test Execution Summary ===");
        spdlog::info("Total Tests: {}", result.total_tests);
        spdlog::info("Passed: {}", result.passed_tests);
        spdlog::info("Failed: {}", result.failed_tests);
        spdlog::info("Skipped: {}", result.skipped_tests);
        spdlog::info("Errors: {}", result.error_tests);
        spdlog::info("Success Rate: {:.1f}%", result.getOverallSuccessRate());
        spdlog::info("Total Duration: {}ms", result.total_duration.count());
        
        return result.isSuccess() ? 0 : 1;
        
    } catch (const std::exception& e) {
        spdlog::error("Fatal exception in test runner: {}", e.what());
        return -1;
    }
}

} // namespace scr5000
