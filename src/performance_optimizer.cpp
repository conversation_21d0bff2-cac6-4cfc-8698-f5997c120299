#include "performance_optimizer.hpp"
#include <algorithm>
#include <cstring>

namespace scr5000 {

// ==================== 性能优化管理器 ====================
class PerformanceOptimizationManager {
private:
    // 内存池实例
    std::unique_ptr<CudaHighPerformancePool<float>> cuda_float_pool_;
    std::unique_ptr<CudaHighPerformancePool<int>> cuda_int_pool_;
    std::unique_ptr<HostHighPerformancePool<float>> host_float_pool_;
    std::unique_ptr<HostHighPerformancePool<int>> host_int_pool_;
    
    // 性能监控
    std::atomic<size_t> total_cuda_allocations_;
    std::atomic<size_t> total_host_allocations_;
    std::chrono::steady_clock::time_point start_time_;
    
    mutable std::mutex mutex_;
    bool initialized_;
    
    // 单例模式
    static std::unique_ptr<PerformanceOptimizationManager> instance_;
    static std::mutex instance_mutex_;
    
    PerformanceOptimizationManager() 
        : total_cuda_allocations_(0), total_host_allocations_(0),
          start_time_(std::chrono::steady_clock::now()), initialized_(false) {}

public:
    static PerformanceOptimizationManager* getInstance() {
        std::lock_guard<std::mutex> lock(instance_mutex_);
        if (!instance_) {
            instance_ = std::unique_ptr<PerformanceOptimizationManager>(
                new PerformanceOptimizationManager());
        }
        return instance_.get();
    }
    
    static void releaseInstance() {
        std::lock_guard<std::mutex> lock(instance_mutex_);
        instance_.reset();
    }
    
    bool initialize() {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (initialized_) {
            spdlog::warn("Performance optimization manager already initialized");
            return true;
        }
        
        try {
            spdlog::info("Initializing performance optimization manager...");
            
            // 创建CUDA内存池
            cuda_float_pool_ = std::make_unique<CudaHighPerformancePool<float>>(512 * 1024 * 1024);
            cuda_int_pool_ = std::make_unique<CudaHighPerformancePool<int>>(256 * 1024 * 1024);
            
            // 创建主机内存池
            host_float_pool_ = std::make_unique<HostHighPerformancePool<float>>(1024 * 1024 * 1024);
            host_int_pool_ = std::make_unique<HostHighPerformancePool<int>>(512 * 1024 * 1024);
            
            // 预分配常用大小
            cuda_float_pool_->preallocateCommonSizes();
            cuda_int_pool_->preallocateCommonSizes();
            host_float_pool_->preallocateCommonSizes();
            host_int_pool_->preallocateCommonSizes();
            
            initialized_ = true;
            spdlog::info("Performance optimization manager initialized successfully");
            return true;
            
        } catch (const std::exception& e) {
            spdlog::error("Failed to initialize performance optimization manager: {}", e.what());
            cleanup();
            return false;
        }
    }
    
    void cleanup() {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (!initialized_) return;
        
        spdlog::info("Cleaning up performance optimization manager...");
        
        // 记录最终统计信息
        logFinalStatistics();
        
        // 清理内存池
        cuda_float_pool_.reset();
        cuda_int_pool_.reset();
        host_float_pool_.reset();
        host_int_pool_.reset();
        
        initialized_ = false;
        spdlog::info("Performance optimization manager cleanup completed");
    }
    
    // CUDA内存分配接口
    float* allocateCudaFloat(size_t count) {
        if (!initialized_) {
            spdlog::error("Performance optimization manager not initialized");
            return nullptr;
        }
        
        total_cuda_allocations_.fetch_add(1);
        return cuda_float_pool_->allocate(count);
    }
    
    void deallocateCudaFloat(float* ptr) {
        if (initialized_ && cuda_float_pool_) {
            cuda_float_pool_->deallocate(ptr);
        }
    }
    
    int* allocateCudaInt(size_t count) {
        if (!initialized_) {
            spdlog::error("Performance optimization manager not initialized");
            return nullptr;
        }
        
        total_cuda_allocations_.fetch_add(1);
        return cuda_int_pool_->allocate(count);
    }
    
    void deallocateCudaInt(int* ptr) {
        if (initialized_ && cuda_int_pool_) {
            cuda_int_pool_->deallocate(ptr);
        }
    }
    
    // 主机内存分配接口
    float* allocateHostFloat(size_t count) {
        if (!initialized_) {
            spdlog::error("Performance optimization manager not initialized");
            return nullptr;
        }
        
        total_host_allocations_.fetch_add(1);
        return host_float_pool_->allocate(count);
    }
    
    void deallocateHostFloat(float* ptr) {
        if (initialized_ && host_float_pool_) {
            host_float_pool_->deallocate(ptr);
        }
    }
    
    int* allocateHostInt(size_t count) {
        if (!initialized_) {
            spdlog::error("Performance optimization manager not initialized");
            return nullptr;
        }
        
        total_host_allocations_.fetch_add(1);
        return host_int_pool_->allocate(count);
    }
    
    void deallocateHostInt(int* ptr) {
        if (initialized_ && host_int_pool_) {
            host_int_pool_->deallocate(ptr);
        }
    }
    
    // 性能统计
    void logPerformanceStatistics() const {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (!initialized_) {
            spdlog::warn("Cannot log statistics: manager not initialized");
            return;
        }
        
        auto now = std::chrono::steady_clock::now();
        auto uptime = std::chrono::duration_cast<std::chrono::seconds>(now - start_time_).count();
        
        spdlog::info("=== Performance Optimization Statistics ===");
        spdlog::info("Uptime: {} seconds", uptime);
        spdlog::info("Total CUDA allocations: {}", total_cuda_allocations_.load());
        spdlog::info("Total Host allocations: {}", total_host_allocations_.load());
        
        // CUDA Float池统计
        auto cuda_float_stats = cuda_float_pool_->getStatistics();
        spdlog::info("CUDA Float Pool:");
        spdlog::info("  Allocated: {:.1f}MB, Used: {:.1f}MB, Utilization: {:.1f}%",
                    cuda_float_stats.total_allocated_bytes / (1024.0*1024.0),
                    cuda_float_stats.total_used_bytes / (1024.0*1024.0),
                    cuda_float_stats.utilization_ratio * 100.0);
        spdlog::info("  Blocks: {} total, {} free, Cache hit ratio: {:.1f}%",
                    cuda_float_stats.num_blocks, cuda_float_stats.num_free_blocks,
                    cuda_float_stats.cache_hit_ratio * 100.0);
        
        // CUDA Int池统计
        auto cuda_int_stats = cuda_int_pool_->getStatistics();
        spdlog::info("CUDA Int Pool:");
        spdlog::info("  Allocated: {:.1f}MB, Used: {:.1f}MB, Utilization: {:.1f}%",
                    cuda_int_stats.total_allocated_bytes / (1024.0*1024.0),
                    cuda_int_stats.total_used_bytes / (1024.0*1024.0),
                    cuda_int_stats.utilization_ratio * 100.0);
        spdlog::info("  Blocks: {} total, {} free, Cache hit ratio: {:.1f}%",
                    cuda_int_stats.num_blocks, cuda_int_stats.num_free_blocks,
                    cuda_int_stats.cache_hit_ratio * 100.0);
        
        // Host Float池统计
        auto host_float_stats = host_float_pool_->getStatistics();
        spdlog::info("Host Float Pool:");
        spdlog::info("  Allocated: {:.1f}MB, Used: {:.1f}MB, Utilization: {:.1f}%",
                    host_float_stats.total_allocated_bytes / (1024.0*1024.0),
                    host_float_stats.total_used_bytes / (1024.0*1024.0),
                    host_float_stats.utilization_ratio * 100.0);
        spdlog::info("  Blocks: {} total, {} free, Cache hit ratio: {:.1f}%",
                    host_float_stats.num_blocks, host_float_stats.num_free_blocks,
                    host_float_stats.cache_hit_ratio * 100.0);
        
        // Host Int池统计
        auto host_int_stats = host_int_pool_->getStatistics();
        spdlog::info("Host Int Pool:");
        spdlog::info("  Allocated: {:.1f}MB, Used: {:.1f}MB, Utilization: {:.1f}%",
                    host_int_stats.total_allocated_bytes / (1024.0*1024.0),
                    host_int_stats.total_used_bytes / (1024.0*1024.0),
                    host_int_stats.utilization_ratio * 100.0);
        spdlog::info("  Blocks: {} total, {} free, Cache hit ratio: {:.1f}%",
                    host_int_stats.num_blocks, host_int_stats.num_free_blocks,
                    host_int_stats.cache_hit_ratio * 100.0);
    }
    
    // 内存整理
    void defragmentAllPools() {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (!initialized_) {
            spdlog::warn("Cannot defragment: manager not initialized");
            return;
        }
        
        spdlog::info("Starting defragmentation of all memory pools...");
        
        cuda_float_pool_->defragment();
        cuda_int_pool_->defragment();
        host_float_pool_->defragment();
        host_int_pool_->defragment();
        
        spdlog::info("All memory pools defragmented");
    }
    
    bool isInitialized() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return initialized_;
    }

private:
    void logFinalStatistics() const {
        spdlog::info("=== Final Performance Statistics ===");
        
        auto now = std::chrono::steady_clock::now();
        auto total_uptime = std::chrono::duration_cast<std::chrono::seconds>(now - start_time_).count();
        
        spdlog::info("Total uptime: {} seconds", total_uptime);
        spdlog::info("Total CUDA allocations: {}", total_cuda_allocations_.load());
        spdlog::info("Total Host allocations: {}", total_host_allocations_.load());
        
        if (total_uptime > 0) {
            double cuda_alloc_rate = static_cast<double>(total_cuda_allocations_.load()) / total_uptime;
            double host_alloc_rate = static_cast<double>(total_host_allocations_.load()) / total_uptime;
            
            spdlog::info("CUDA allocation rate: {:.2f} allocs/sec", cuda_alloc_rate);
            spdlog::info("Host allocation rate: {:.2f} allocs/sec", host_alloc_rate);
        }
    }
};

// 静态成员定义
std::unique_ptr<PerformanceOptimizationManager> PerformanceOptimizationManager::instance_ = nullptr;
std::mutex PerformanceOptimizationManager::instance_mutex_;

// ==================== 全局接口函数 ====================
bool initializePerformanceOptimization() {
    auto* manager = PerformanceOptimizationManager::getInstance();
    return manager->initialize();
}

void cleanupPerformanceOptimization() {
    auto* manager = PerformanceOptimizationManager::getInstance();
    if (manager) {
        manager->cleanup();
    }
    PerformanceOptimizationManager::releaseInstance();
}

bool isPerformanceOptimizationInitialized() {
    auto* manager = PerformanceOptimizationManager::getInstance();
    return manager->isInitialized();
}

void logPerformanceStatistics() {
    auto* manager = PerformanceOptimizationManager::getInstance();
    if (manager) {
        manager->logPerformanceStatistics();
    }
}

void defragmentMemoryPools() {
    auto* manager = PerformanceOptimizationManager::getInstance();
    if (manager) {
        manager->defragmentAllPools();
    }
}

// ==================== 便利的分配函数 ====================
float* allocateOptimizedCudaFloat(size_t count) {
    auto* manager = PerformanceOptimizationManager::getInstance();
    return manager ? manager->allocateCudaFloat(count) : nullptr;
}

void deallocateOptimizedCudaFloat(float* ptr) {
    auto* manager = PerformanceOptimizationManager::getInstance();
    if (manager) {
        manager->deallocateCudaFloat(ptr);
    }
}

int* allocateOptimizedCudaInt(size_t count) {
    auto* manager = PerformanceOptimizationManager::getInstance();
    return manager ? manager->allocateCudaInt(count) : nullptr;
}

void deallocateOptimizedCudaInt(int* ptr) {
    auto* manager = PerformanceOptimizationManager::getInstance();
    if (manager) {
        manager->deallocateCudaInt(ptr);
    }
}

float* allocateOptimizedHostFloat(size_t count) {
    auto* manager = PerformanceOptimizationManager::getInstance();
    return manager ? manager->allocateHostFloat(count) : nullptr;
}

void deallocateOptimizedHostFloat(float* ptr) {
    auto* manager = PerformanceOptimizationManager::getInstance();
    if (manager) {
        manager->deallocateHostFloat(ptr);
    }
}

int* allocateOptimizedHostInt(size_t count) {
    auto* manager = PerformanceOptimizationManager::getInstance();
    return manager ? manager->allocateHostInt(count) : nullptr;
}

void deallocateOptimizedHostInt(int* ptr) {
    auto* manager = PerformanceOptimizationManager::getInstance();
    if (manager) {
        manager->deallocateHostInt(ptr);
    }
}

} // namespace scr5000
