#include "fft_gpu.hpp"
#include <stdexcept>
#include <sstream>

FFTGPUOptimizer::FFTGPUOptimizer(int rows, int cols)
    : ROWS(rows), COLS(cols), gpu_manager_(nullptr) {

    try {
        spdlog::info("Initializing FFT GPU Optimizer: {}x{}", rows, cols);

        // 获取GPU资源管理器
        gpu_manager_ = scr5000::getGPUResourceManager();
        if (!gpu_manager_ || !gpu_manager_->isInitialized()) {
            throw std::runtime_error("GPU resource manager not available or not initialized");
        }

        // 初始化资源池
        if (!initializeResourcePools()) {
            throw std::runtime_error("Failed to initialize FFT resource pools");
        }

        spdlog::info("FFT GPU Optimizer initialized successfully");

    } catch (const std::exception& e) {
        spdlog::error("FFT GPU Optimizer initialization failed: {}", e.what());
        cleanup();
        throw;
    }
}

FFTGPUOptimizer::FFTGPUOptimizer(int rows, int cols, scr5000::GPUResourceManager* gpu_manager)
    : ROWS(rows), COLS(cols), gpu_manager_(gpu_manager) {

    try {
        spdlog::info("Initializing FFT GPU Optimizer with provided GPU manager: {}x{}", rows, cols);

        if (!gpu_manager_ || !gpu_manager_->isInitialized()) {
            throw std::runtime_error("Provided GPU resource manager not available or not initialized");
        }

        // 初始化资源池
        if (!initializeResourcePools()) {
            throw std::runtime_error("Failed to initialize FFT resource pools");
        }

        spdlog::info("FFT GPU Optimizer initialized successfully with provided GPU manager");

    } catch (const std::exception& e) {
        spdlog::error("FFT GPU Optimizer initialization failed: {}", e.what());
        cleanup();
        throw;
    }
}

FFTGPUOptimizer::~FFTGPUOptimizer() {
    cleanup();
}

bool FFTGPUOptimizer::initializeResourcePools() {
    if (!gpu_manager_) {
        spdlog::error("GPU resource manager not available");
        return false;
    }

    spdlog::info("Initializing FFT resource pools for 5 segment lengths");

    // 为每个预定义长度初始化资源池
    for (size_t i = 0; i < SEGMENT_LENGTHS.size(); ++i) {
        int length = SEGMENT_LENGTHS[i];
        auto& pool = resource_pools_[i];
        pool.length = length;

        spdlog::debug("Initializing resource pool for length: {}", length);

        // 为每个长度创建10个缓冲区和10个FFT计划
        for (int j = 0; j < BUFFERS_PER_LENGTH; ++j) {
            // 创建GPU内存缓冲区
            pool.buffers[j] = std::make_unique<scr5000::CudaMemoryRAII<cufftComplex>>(length);
            if (!pool.buffers[j]->is_valid()) {
                spdlog::error("Failed to allocate GPU buffer for length {} index {}", length, j);
                return false;
            }

            // 创建FFT计划
            pool.plans[j] = std::make_unique<scr5000::CuFFTPlanRAII>();
            if (!pool.plans[j]->create1D(length, CUFFT_C2C, 1)) {
                spdlog::error("Failed to create FFT plan for length {} index {}", length, j);
                return false;
            }

            pool.buffer_in_use[j] = false;
        }

        spdlog::info("Resource pool initialized for length {}: {} buffers, {} plans",
                    length, BUFFERS_PER_LENGTH, BUFFERS_PER_LENGTH);
    }

    spdlog::info("All FFT resource pools initialized successfully");
    return true;
}

void FFTGPUOptimizer::cleanup() {
    spdlog::debug("Cleaning up FFT GPU Optimizer resources");

    // 清理所有资源池
    for (auto& pool : resource_pools_) {
        for (int i = 0; i < BUFFERS_PER_LENGTH; ++i) {
            pool.buffers[i].reset();
            pool.plans[i].reset();
            pool.buffer_in_use[i] = false;
        }
        pool.length = 0;
    }

    // 注意：不直接清理GPU管理器，它有自己的生命周期
    gpu_manager_ = nullptr;

    spdlog::debug("FFT GPU Optimizer cleanup completed");
}

int FFTGPUOptimizer::findClosestLengthIndex(int segment_length) const {
    // 找到最接近且不小于segment_length的预定义长度
    for (size_t i = SEGMENT_LENGTHS.size(); i > 0; --i) {
        if (segment_length <= SEGMENT_LENGTHS[i-1]) {
            return static_cast<int>(i-1);
        }
    }
    // 如果都不满足，使用最大的长度
    return 0;
}

std::pair<scr5000::CudaMemoryRAII<cufftComplex>*, scr5000::CuFFTPlanRAII*>
FFTGPUOptimizer::acquireResources(int segment_length) {
    int pool_index = findClosestLengthIndex(segment_length);
    auto& pool = resource_pools_[pool_index];

    // 查找可用的资源
    for (int i = 0; i < BUFFERS_PER_LENGTH; ++i) {
        if (!pool.buffer_in_use[i]) {
            pool.buffer_in_use[i] = true;
            spdlog::debug("Acquired resources from pool {} (length {}), slot {}",
                         pool_index, pool.length, i);
            return {pool.buffers[i].get(), pool.plans[i].get()};
        }
    }

    spdlog::warn("No available resources in pool {} (length {}), using slot 0",
                pool_index, pool.length);
    // 如果没有可用资源，强制使用第一个（可能会有并发问题，但作为备用方案）
    return {pool.buffers[0].get(), pool.plans[0].get()};
}

void FFTGPUOptimizer::releaseResources(scr5000::CudaMemoryRAII<cufftComplex>* buffer) {
    if (!buffer) return;

    // 查找并释放资源
    for (auto& pool : resource_pools_) {
        for (int i = 0; i < BUFFERS_PER_LENGTH; ++i) {
            if (pool.buffers[i].get() == buffer) {
                pool.buffer_in_use[i] = false;
                spdlog::debug("Released resources in pool (length {}), slot {}", pool.length, i);
                return;
            }
        }
    }

    spdlog::warn("Attempt to release unknown buffer");
}

std::string FFTGPUOptimizer::getResourceInfo() const {
    std::ostringstream oss;
    oss << "FFT GPU Optimizer Status:\n";
    oss << "  Dimensions: " << ROWS << "x" << COLS << "\n";
    oss << "  GPU Manager: " << (gpu_manager_ ? "Available" : "Not Available") << "\n";
    oss << "  Resource Pools: " << SEGMENT_LENGTHS.size() << "\n";

    size_t total_memory = 0;
    int total_buffers = 0;
    int buffers_in_use = 0;

    for (size_t i = 0; i < SEGMENT_LENGTHS.size(); ++i) {
        const auto& pool = resource_pools_[i];
        int pool_in_use = 0;

        for (int j = 0; j < BUFFERS_PER_LENGTH; ++j) {
            if (pool.buffers[j] && pool.buffers[j]->is_valid()) {
                total_memory += pool.buffers[j]->size_bytes();
                total_buffers++;
                if (pool.buffer_in_use[j]) {
                    pool_in_use++;
                    buffers_in_use++;
                }
            }
        }

        oss << "    Pool " << i << " (length " << pool.length << "): "
            << pool_in_use << "/" << BUFFERS_PER_LENGTH << " in use\n";
    }

    oss << "  Total Buffers: " << total_buffers << " (" << buffers_in_use << " in use)\n";
    oss << "  Total Memory: " << total_memory / (1024.0 * 1024.0) << " MB\n";

    return oss.str();
}

// 对单个列数据段执行FFT - 使用预分配的资源池
std::complex<float> FFTGPUOptimizer::performFFTOnSegment(
    const std::vector<cufftComplex>& segment_data,
    int target_row,
    int segment_start)
{
    int segment_length = segment_data.size();

    if (segment_length <= 0) {
        throw std::runtime_error("Invalid segment length: " + std::to_string(segment_length));
    }

    // 检查CUDA上下文状态
    cudaError_t status = cudaGetLastError();
    if (status != cudaSuccess) {
        throw std::runtime_error("CUDA context error before FFT: " +
                                std::string(cudaGetErrorString(status)));
    }

    try {
        // 从资源池获取预分配的资源
        auto [buffer, plan] = acquireResources(segment_length);
        if (!buffer || !plan || !buffer->is_valid() || !plan->is_valid()) {
            throw std::runtime_error("Failed to acquire FFT resources");
        }

        // 获取实际的缓冲区长度（可能比segment_length大）
        int buffer_length = buffer->count();

        // 清零缓冲区（确保未使用部分为0）
        cudaError_t cuda_status = cudaMemset(buffer->get(), 0, buffer_length * sizeof(cufftComplex));
        if (cuda_status != cudaSuccess) {
            releaseResources(buffer);
            throw std::runtime_error("CUDA memset failed: " + std::string(cudaGetErrorString(cuda_status)));
        }

        // 拷贝数据到设备（只拷贝实际数据长度）
        cuda_status = cudaMemcpy(
            buffer->get(),
            segment_data.data(),
            segment_length * sizeof(cufftComplex),
            cudaMemcpyHostToDevice
        );
        if (cuda_status != cudaSuccess) {
            releaseResources(buffer);
            throw std::runtime_error("CUDA memcpy to device failed: " + std::string(cudaGetErrorString(cuda_status)));
        }

        // 执行FFT（使用缓冲区的完整长度）
        cufftResult fft_status = cufftExecC2C(
            plan->get(),
            static_cast<cufftComplex*>(buffer->get()),
            static_cast<cufftComplex*>(buffer->get()),  // 原地FFT
            CUFFT_FORWARD
        );
        if (fft_status != CUFFT_SUCCESS) {
            releaseResources(buffer);
            throw std::runtime_error("cuFFT execution failed: " + std::to_string(static_cast<int>(fft_status)));
        }

        // 只取回目标位置的结果
        int local_row = target_row - segment_start;
        if (local_row < 0 || local_row >= segment_length) {
            releaseResources(buffer);
            throw std::runtime_error("Target row out of segment bounds: " + std::to_string(local_row));
        }

        cufftComplex result_data;
        cuda_status = cudaMemcpy(
            &result_data,
            static_cast<cufftComplex*>(buffer->get()) + local_row,
            sizeof(cufftComplex),
            cudaMemcpyDeviceToHost
        );

        // 释放资源
        releaseResources(buffer);

        if (cuda_status != cudaSuccess) {
            throw std::runtime_error("CUDA memcpy from device failed: " + std::string(cudaGetErrorString(cuda_status)));
        }

        return std::complex<float>(result_data.x, result_data.y);

    } catch (const std::exception& e) {
        throw std::runtime_error("FFT processing failed: " + std::string(e.what()));
    }
}