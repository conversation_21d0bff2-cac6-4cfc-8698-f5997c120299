{
    "version": "0.2.0",
    "configurations": [
      {
        "name": "GDB Launch MSHNet_TensorRT_Test",
        "type": "cppdbg",
        "request": "launch",
        // "program": "${workspaceFolder}/build/MSHNet_TensorRT_Test",
        "program": "${workspaceFolder}/build/test_algorithm",
        // "program": "${workspaceFolder}/data_parse_dataset/01_data_parse",
        "args": [],
        "stopAtEntry": false,
        "cwd": "${workspaceFolder}",
        "environment": [
          {
            "name": "TENSORRT_ROOT",
            "value": "/home/<USER>/My_APP/TensorRT-*******"
          }
        ],
        "externalConsole": false,
        "MIMode": "gdb",
        "miDebuggerPath": "/usr/bin/gdb",
        "setupCommands": [
          {
            "description": "Enable pretty-printing for gdb",
            "text": "-enable-pretty-printing",
            "ignoreFailures": true
          }
        ],
        "preLaunchTask": "CMake Build"
      }
    ]
  }