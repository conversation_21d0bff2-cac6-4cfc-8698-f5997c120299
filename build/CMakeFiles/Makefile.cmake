# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.25

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/home/<USER>/My_Project/MSHNet_TensorRT_Test/CMakeLists.txt"
  "CMakeFiles/3.25.0/CMakeCCompiler.cmake"
  "CMakeFiles/3.25.0/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.25.0/CMakeSystem.cmake"
  "/home/<USER>/My_Project/MSHNet_TensorRT_Test/include/TutorialConfig.h.in"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/CMakeCInformation.cmake"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/CMakeCXXInformation.cmake"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/CMakeCommonLanguageInclude.cmake"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/CMakeGenericSystem.cmake"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/CMakeInitializeConfigs.cmake"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/CMakeLanguageInformation.cmake"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/CMakeParseImplicitLinkInfo.cmake"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/CMakeSystemSpecificInformation.cmake"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/CMakeSystemSpecificInitialize.cmake"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/CheckCSourceCompiles.cmake"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/CheckIncludeFile.cmake"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/CheckLibraryExists.cmake"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/Compiler/GNU-C.cmake"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/Compiler/GNU-CXX.cmake"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/Compiler/GNU.cmake"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/FindCUDA.cmake"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/FindCUDA/select_compute_arch.cmake"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/FindOpenMP.cmake"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/FindPackageHandleStandardArgs.cmake"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/FindPackageMessage.cmake"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/FindThreads.cmake"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/Internal/CheckSourceCompiles.cmake"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/Platform/Linux-GNU-C.cmake"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/Platform/Linux-GNU-CXX.cmake"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/Platform/Linux-GNU.cmake"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/Platform/Linux.cmake"
  "/home/<USER>/miniconda3/envs/pytorch/lib/python3.8/site-packages/cmake/data/share/cmake-3.25/Modules/Platform/UnixPaths.cmake"
  "/usr/local/lib/cmake/opencv4/OpenCVConfig-version.cmake"
  "/usr/local/lib/cmake/opencv4/OpenCVConfig.cmake"
  "/usr/local/lib/cmake/opencv4/OpenCVModules-release.cmake"
  "/usr/local/lib/cmake/opencv4/OpenCVModules.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "/home/<USER>/My_Project/MSHNet_TensorRT_Test/include/TutorialConfig.h"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/SCR_5000_AI.dir/DependInfo.cmake"
  "CMakeFiles/test_algorithm.dir/DependInfo.cmake"
  )
