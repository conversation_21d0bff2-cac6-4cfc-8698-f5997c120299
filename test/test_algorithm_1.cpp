#include "libSCR_5000_Alg.hpp"
#include <iostream>
#include <vector>
#include <string>
#include <fstream>
#include <chrono>
#include <cstring>
#include <iomanip>
#include <algorithm>
#include <dirent.h>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <atomic>
#include <memory>
#include <exception>
#include <stdexcept>
#include <spdlog/spdlog.h>
#include <csignal>
#include <sys/resource.h>

// ========= 常量 =========
const int ROWS = 1024;
const int COLS = 2048;

// ========= 资源监控类 =========
class ResourceMonitor {
private:
    std::chrono::steady_clock::time_point start_time_;
    std::atomic<size_t> processed_frames_;
    std::atomic<size_t> failed_frames_;
    std::atomic<bool> monitoring_;
    std::thread monitor_thread_;

public:
    ResourceMonitor() : processed_frames_(0), failed_frames_(0), monitoring_(false) {
        start_time_ = std::chrono::steady_clock::now();
    }

    ~ResourceMonitor() {
        stop();
    }

    void start() {
        if (monitoring_.load()) return;

        monitoring_.store(true);
        monitor_thread_ = std::thread([this]() {
            while (monitoring_.load()) {
                logResourceUsage();
                std::this_thread::sleep_for(std::chrono::seconds(10));
            }
        });

        spdlog::info("Resource monitor started");
    }

    void stop() {
        if (!monitoring_.load()) return;

        monitoring_.store(false);
        if (monitor_thread_.joinable()) {
            monitor_thread_.join();
        }

        logFinalStats();
        spdlog::info("Resource monitor stopped");
    }

    void incrementProcessedFrames() {
        processed_frames_.fetch_add(1);
    }

    void incrementFailedFrames() {
        failed_frames_.fetch_add(1);
    }

private:
    void logResourceUsage() {
        try {
            // 获取内存使用情况
            struct rusage usage;
            if (getrusage(RUSAGE_SELF, &usage) == 0) {
                spdlog::info("Memory usage: {}MB RSS, {}MB peak",
                           usage.ru_maxrss / 1024, usage.ru_maxrss / 1024);
            }

            // 注意：这里可以添加更多的资源监控逻辑
            // 由于测试程序不直接访问内部资源管理器，我们使用系统级监控

            // 处理统计
            auto now = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - start_time_).count();
            if (elapsed > 0) {
                double fps = static_cast<double>(processed_frames_.load()) / elapsed;
                spdlog::info("Processing stats: {} frames processed, {} failed, {:.2f} FPS",
                           processed_frames_.load(), failed_frames_.load(), fps);
            }

        } catch (const std::exception& e) {
            spdlog::error("Error in resource monitoring: {}", e.what());
        }
    }

    void logFinalStats() {
        auto end_time = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(end_time - start_time_).count();

        spdlog::info("=== Final Statistics ===");
        spdlog::info("Total runtime: {} seconds", elapsed);
        spdlog::info("Frames processed: {}", processed_frames_.load());
        spdlog::info("Frames failed: {}", failed_frames_.load());

        if (elapsed > 0) {
            double fps = static_cast<double>(processed_frames_.load()) / elapsed;
            double success_rate = processed_frames_.load() > 0 ?
                static_cast<double>(processed_frames_.load()) /
                (processed_frames_.load() + failed_frames_.load()) * 100.0 : 0.0;

            spdlog::info("Average FPS: {:.2f}", fps);
            spdlog::info("Success rate: {:.1f}%", success_rate);
        }
    }
};

// ========= 文件名排序 =========
bool compareFilesByNumber(const std::string& a, const std::string& b) {
    auto extractBaseName = [](const std::string& path) {
        size_t lastSlash = path.find_last_of("/\\");
        size_t lastDot = path.find_last_of('.');
        if (lastDot == std::string::npos) lastDot = path.size();
        return path.substr(lastSlash + 1, lastDot - lastSlash - 1);
    };
    std::string baseA = extractBaseName(a);
    std::string baseB = extractBaseName(b);
    size_t posA = 0, posB = 0;
    while (posA < baseA.size() && posB < baseB.size()) {
        auto getNumber = [](const std::string& s, size_t& pos) {
            long num = 0;
            while (pos < s.size() && isdigit(s[pos])) {
                num = num * 10 + (s[pos] - '0');
                pos++;
            }
            if (pos < s.size() && s[pos] == '_') pos++;
            return num;
        };
        long numA = getNumber(baseA, posA);
        long numB = getNumber(baseB, posB);
        if (numA != numB) return numA < numB;
    }
    return baseA.size() < baseB.size();
}

std::vector<std::string> get_bin_files(const std::string& folder_path) {
    std::vector<std::string> bin_files;
    DIR* dir = opendir(folder_path.c_str());
    if (!dir) {
        std::cerr << "Cannot open directory: " << folder_path << std::endl;
        return bin_files;
    }
    dirent* ent;
    while ((ent = readdir(dir))) {
        std::string name(ent->d_name);
        if (name.size() > 4 && name.substr(name.size() - 4) == ".bin")
            bin_files.push_back(folder_path + "/" + name);
    }
    closedir(dir);
    std::sort(bin_files.begin(), bin_files.end(), compareFilesByNumber);
    return bin_files;
}

// ========= 异常安全的数据加载类 =========
class RadarDataLoader {
private:
    static constexpr size_t S_head_bytes = 1024 * 40;
    static constexpr size_t per_frame_head = S_head_bytes * 2;
    static constexpr size_t S_data_bytes = 1024 * 2048 * 2 * sizeof(float);
    static constexpr size_t per_frame_data = S_data_bytes * 2;

public:
    struct LoadedData {
        std::vector<char> buffer;
        char* head_data = nullptr;
        char* body_data = nullptr;
        size_t data_size = 0;

        LoadedData() = default;

        // 移动构造和赋值
        LoadedData(LoadedData&& other) noexcept
            : buffer(std::move(other.buffer)),
              head_data(other.head_data),
              body_data(other.body_data),
              data_size(other.data_size) {
            other.head_data = nullptr;
            other.body_data = nullptr;
            other.data_size = 0;
        }

        LoadedData& operator=(LoadedData&& other) noexcept {
            if (this != &other) {
                buffer = std::move(other.buffer);
                head_data = other.head_data;
                body_data = other.body_data;
                data_size = other.data_size;
                other.head_data = nullptr;
                other.body_data = nullptr;
                other.data_size = 0;
            }
            return *this;
        }

        // 禁止拷贝
        LoadedData(const LoadedData&) = delete;
        LoadedData& operator=(const LoadedData&) = delete;

        bool isValid() const {
            return head_data != nullptr && body_data != nullptr && data_size > 0;
        }
    };

    static std::unique_ptr<LoadedData> loadFromFiles(const std::vector<std::string>& file_paths) {
        if (file_paths.size() != 3) {
            throw std::invalid_argument("Exactly 3 files required");
        }

        auto data = std::make_unique<LoadedData>();

        try {
            std::vector<char> headers;
            std::vector<char> datas;

            headers.reserve(per_frame_head * 3);
            datas.reserve(per_frame_data * 3);

            for (const auto& file_path : file_paths) {
                loadSingleFile(file_path, headers, datas);
            }

            // 合并数据
            data->buffer.clear();
            data->buffer.reserve(headers.size() + datas.size());
            data->buffer.insert(data->buffer.end(), headers.begin(), headers.end());
            data->buffer.insert(data->buffer.end(), datas.begin(), datas.end());

            // 设置指针
            data->head_data = data->buffer.data();
            data->body_data = data->buffer.data() + headers.size();
            data->data_size = datas.size();

            spdlog::debug("Loaded radar data: {} header bytes, {} data bytes",
                         headers.size(), datas.size());

            return data;

        } catch (const std::exception& e) {
            spdlog::error("Failed to load radar data: {}", e.what());
            throw;
        }
    }

private:
    static void loadSingleFile(const std::string& file_path,
                              std::vector<char>& headers,
                              std::vector<char>& datas) {
        std::ifstream file(file_path, std::ios::binary | std::ios::ate);
        if (!file) {
            throw std::runtime_error("Cannot open file: " + file_path);
        }

        std::streamsize file_size = file.tellg();
        file.seekg(0, std::ios::beg);

        if (static_cast<size_t>(file_size) < per_frame_head + per_frame_data) {
            throw std::runtime_error("File too small: " + file_path);
        }

        std::vector<char> file_buffer(static_cast<size_t>(file_size));
        if (!file.read(file_buffer.data(), file_size)) {
            throw std::runtime_error("Failed to read file: " + file_path);
        }

        // 提取头部和数据
        headers.insert(headers.end(),
                      file_buffer.data(),
                      file_buffer.data() + per_frame_head);

        datas.insert(datas.end(),
                    file_buffer.data() + per_frame_head,
                    file_buffer.data() + per_frame_head + per_frame_data);

        spdlog::debug("Loaded file: {} ({} bytes)", file_path, file_size);
    }
};

// ========= 打印函数 =========
void PrintDetectionResults(const DetectionResult* results, int n) {
    std::cout << "=== Detection ===\n";
    for (int i = 0; i < std::min(n, 5); ++i)
        std::cout << "  " << i << ": row=" << results[i].row
                  << " col=" << results[i].col << " frame=" << results[i].frame << "\n";
}
void PrintTrackingResults(const TrackingResult* results, int n) {
    std::cout << "=== Tracking ===\n";
    for (int i = 0; i < std::min(n, 5); ++i)
        std::cout << "  id=" << results[i].id
                  << " x=" << results[i].x << " y=" << results[i].y
                  << " z=" << results[i].z << "\n";
}

// ========= 线程安全队列 =========
template<typename T>
class ThreadSafeQueue {
public:
    void push(T item) {
        std::lock_guard<std::mutex> lk(m_);
        q_.push(std::move(item));
        cv_.notify_one();
    }
    bool pop(T& item, std::chrono::milliseconds timeout = std::chrono::milliseconds(100)) {
        std::unique_lock<std::mutex> lk(m_);
        if (!cv_.wait_for(lk, timeout, [this] { return !q_.empty() || stop_; })) return false;
        if (stop_) return false;
        item = std::move(q_.front());
        q_.pop();
        return true;
    }
    void shutdown() {
        std::lock_guard<std::mutex> lk(m_);
        stop_ = true;
        cv_.notify_all();
    }
private:
    std::queue<T> q_;
    std::mutex m_;
    std::condition_variable cv_;
    bool stop_ = false;
};

// ========= 现代化的帧数据包 =========
class FramePacket {
private:
    std::unique_ptr<RadarDataLoader::LoadedData> data_;

public:
    FramePacket() = default;

    explicit FramePacket(std::unique_ptr<RadarDataLoader::LoadedData> data)
        : data_(std::move(data)) {}

    // 移动构造和赋值
    FramePacket(FramePacket&& other) noexcept
        : data_(std::move(other.data_)) {}

    FramePacket& operator=(FramePacket&& other) noexcept {
        if (this != &other) {
            data_ = std::move(other.data_);
        }
        return *this;
    }

    // 禁止拷贝
    FramePacket(const FramePacket&) = delete;
    FramePacket& operator=(const FramePacket&) = delete;

    bool isValid() const {
        return data_ && data_->isValid();
    }

    char* getHeadData() const {
        return data_ ? data_->head_data : nullptr;
    }

    char* getBodyData() const {
        return data_ ? data_->body_data : nullptr;
    }

    size_t getDataSize() const {
        return data_ ? data_->data_size : 0;
    }

    // 创建帧数据包的工厂方法
    static FramePacket create(const std::vector<std::string>& file_paths) {
        try {
            auto data = RadarDataLoader::loadFromFiles(file_paths);
            return FramePacket(std::move(data));
        } catch (const std::exception& e) {
            spdlog::error("Failed to create frame packet: {}", e.what());
            return FramePacket();
        }
    }
};

ThreadSafeQueue<FramePacket> g_queue;
std::atomic<bool> g_stop{false};

// ========= 现代化的生产者线程 =========
void producer(const std::vector<std::string>& files, size_t window, ResourceMonitor& monitor) {
    size_t idx = 0;
    spdlog::info("Producer thread started with window size: {}", window);

    try {
        while (!g_stop.load()) {
            try {
                std::vector<std::string> group;
                group.reserve(window);

                for (size_t i = 0; i < window; ++i) {
                    group.push_back(files[(idx + i) % files.size()]);
                }

                auto pkt = FramePacket::create(group);
                if (!pkt.isValid()) {
                    spdlog::warn("Failed to create frame packet for group starting at index {}", idx);
                    monitor.incrementFailedFrames();
                    std::this_thread::sleep_for(std::chrono::milliseconds(10));
                    continue;
                }

                g_queue.push(std::move(pkt));
                ++idx;

                // TODO：控制生产速率
                std::this_thread::sleep_for(std::chrono::milliseconds(10));

            } catch (const std::exception& e) {
                spdlog::error("Exception in producer thread: {}", e.what());
                monitor.incrementFailedFrames();
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        }
    } catch (const std::exception& e) {
        spdlog::error("Fatal exception in producer thread: {}", e.what());
    }

    spdlog::info("Producer thread finished");
}

// ========= 现代化的消费者线程 =========
void consumer(ResourceMonitor& monitor) {
    spdlog::info("Consumer thread started");

    try {
        while (!g_stop.load()) {
            try {
                FramePacket pkt;
                if (!g_queue.pop(pkt, std::chrono::milliseconds(100))) {
                    continue;
                }

                if (!pkt.isValid()) {
                    spdlog::warn("Received invalid frame packet");
                    monitor.incrementFailedFrames();
                    continue;
                }

                // 执行目标检测
                InternalDetectionData detection{nullptr, 0, nullptr, nullptr, 0};
                int detection_result = TargetDetection(pkt.getHeadData(), pkt.getBodyData(), &detection);

                if (detection_result != 0) {
                    spdlog::warn("Target detection failed with code: {}", detection_result);
                    monitor.incrementFailedFrames();
                    continue;
                }

                // 执行目标跟踪
                std::vector<TrackingResult> tracks(256);
                int track_count = 0;

                TargetTracking(&detection, tracks.data(), &track_count),
                PrintTrackingResults(tracks.data(), track_count);

                monitor.incrementProcessedFrames();

                // 注意：这里可能需要释放detection数据，取决于API设计
                // ReleaseInternalDetectionData(&detection);

            } catch (const std::exception& e) {
                spdlog::error("Exception in consumer thread: {}", e.what());
                monitor.incrementFailedFrames();
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        }
    } catch (const std::exception& e) {
        spdlog::error("Fatal exception in consumer thread: {}", e.what());
    }

    spdlog::info("Consumer thread finished");
}

// ========= 信号处理 =========
std::atomic<bool> g_signal_received{false};

void signalHandler(int signal) {
    spdlog::warn("Received signal {}, initiating graceful shutdown...", signal);
    g_signal_received.store(true);
    g_stop.store(true);
}

// ========= 现代化的主函数 =========
int main() {
    // 设置日志级别
    spdlog::set_level(spdlog::level::info);
    spdlog::info("=== SCR_5000 Enhanced Async Demo ===");

    // 设置信号处理
    std::signal(SIGINT, signalHandler);
    std::signal(SIGTERM, signalHandler);

    try {
        // 初始化算法库
        spdlog::info("Initializing algorithm library...");
        if (InitializeAlgorithmLibrary("config/custom_config.json") != 0) {
            spdlog::error("Algorithm library initialization failed");
            return -1;
        }
        spdlog::info("Algorithm library initialized successfully");

        // 获取测试文件
        auto files = get_bin_files("data/test1");
        if (files.size() < 3) {
            spdlog::error("Need at least 3 bin files, found: {}", files.size());
            return -1;
        }
        spdlog::info("Found {} test files", files.size());

        // 创建资源监控器
        ResourceMonitor monitor;
        monitor.start();

        // 启动生产者和消费者线程
        spdlog::info("Starting processing threads...");
        std::thread producer_thread(producer, std::cref(files), 3, std::ref(monitor));
        std::thread consumer_thread(consumer, std::ref(monitor));

        // 运行演示
        const int demo_duration_seconds = 60;
        spdlog::info("Running demo for {} seconds...", demo_duration_seconds);

        auto start_time = std::chrono::steady_clock::now();
        while (!g_signal_received.load()) {
            std::this_thread::sleep_for(std::chrono::seconds(1));

            auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
                std::chrono::steady_clock::now() - start_time).count();

            if (elapsed >= demo_duration_seconds) {
                spdlog::info("Demo duration reached, stopping...");
                break;
            }
        }

        // 优雅关闭
        spdlog::info("Initiating graceful shutdown...");
        g_stop.store(true);
        g_queue.shutdown();

        // 等待线程结束
        if (producer_thread.joinable()) {
            producer_thread.join();
        }
        if (consumer_thread.joinable()) {
            consumer_thread.join();
        }

        // 停止监控
        monitor.stop();

        // 释放资源
        spdlog::info("Releasing all resources...");
        ReleaseAllResources();

        spdlog::info("Demo finished successfully");
        return 0;

    } catch (const std::exception& e) {
        spdlog::error("Fatal exception in main: {}", e.what());

        // 确保资源被释放
        try {
            g_stop.store(true);
            g_queue.shutdown();
            ReleaseAllResources();
        } catch (...) {
            spdlog::error("Exception during cleanup");
        }

        return -1;
    } catch (...) {
        spdlog::error("Unknown fatal exception in main");

        // 确保资源被释放
        try {
            g_stop.store(true);
            g_queue.shutdown();
            ReleaseAllResources();
        } catch (...) {
            // 忽略清理时的异常
        }

        return -1;
    }
}