#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Parse runtime log files and plot top 5 longest tracks in 3D.
Each track is shown with points (time gradient: light->dark) and a line.
"""

import os
import glob
import re
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.cm as cm
import numpy as np

# ========== Log directory ==========
log_dir = "test"

# Match runtime.1.log ... runtime.log
log_files = sorted(
    glob.glob(os.path.join(log_dir, "runtime.*.log")),
    key=lambda x: (len(x), x)   # ensure runtime.log at the end
)
final_log = os.path.join(log_dir, "runtime.log")
if final_log not in log_files and os.path.exists(final_log):
    log_files.append(final_log)

if not log_files:
    raise FileNotFoundError("No runtime log files found")

print("Log files to read:", log_files)

# ========== Parse logs ==========
pattern = re.compile(
    r"Track\[\s*\d+\]\s*ID:\s*(\d+).*?Pos:\s*\(\s*([\-0-9\.]+),\s*([\-0-9\.]+),\s*([\-0-9\.]+)\)"
)

tracks = {}   # {id: [(t, x, y, z), ...]}
time_counter = 0

for f in log_files:
    with open(f, "r", encoding="utf-8", errors="ignore") as fh:
        for line in fh:
            m = pattern.search(line)
            if m:
                tid = int(m.group(1))
                x, y, z = map(float, m.group(2, 3, 4))
                if tid not in tracks:
                    tracks[tid] = []
                tracks[tid].append((time_counter, x, y, z))
                time_counter += 1

if not tracks:
    raise ValueError("No track data parsed from logs")

# ========== Select top 5 longest tracks ==========
sorted_tracks = sorted(tracks.items(), key=lambda kv: len(kv[1]), reverse=True)
top_tracks = sorted_tracks[:5]

# ========== Plot ==========
fig = plt.figure(figsize=(12, 8))
ax = fig.add_subplot(111, projection="3d")

# Assign different base colors for different IDs
base_colors = cm.tab10(np.linspace(0, 1, len(top_tracks)))

for (tid, points), base_c in zip(top_tracks, base_colors):
    points = np.array(points)  # shape: (n, 4)
    t = points[:, 0]
    x, y, z = points[:, 1], points[:, 2], points[:, 3]

    # Normalize time for gradient (0 -> 1)
    norm = (t - t.min()) / max(1, (t.max() - t.min()))

    # Gradient colors for scatter points
    colors = [tuple(base_c[:3] * (0.3 + 0.7*v)) for v in norm]

    # Scatter points with gradient
    ax.scatter(x, y, z, c=colors, s=20)

    # Connect with a line (single color = base)
    ax.plot(x, y, z, color=base_c, alpha=0.7, label=f"ID {tid}")

ax.set_xlabel("X")
ax.set_ylabel("Y")
ax.set_zlabel("Z")
ax.set_title("Top 5 Longest Tracks (time: light to dark)")
ax.legend()
plt.show()
