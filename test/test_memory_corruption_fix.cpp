#include <iostream>
#include <cstring>
#include <random>
#include "libSCR_5000_Alg.hpp"

// 测试内存损坏修复的程序
int main() {
    std::cout << "测试内存损坏修复..." << std::endl;
    
    // 测试1: 正常的 InternalDetectionData 初始化
    std::cout << "\n=== 测试1: 正常初始化 ===" << std::endl;
    InternalDetectionData normal_data;
    std::memset(&normal_data, 0, sizeof(InternalDetectionData));
    normal_data.num_detections = 5;
    normal_data.num_segments = 5;
    normal_data.detection_results = new DetectionResult[5];
    normal_data.column_segments = new ColumnSegmentData[5];
    
    bool is_valid = validateDetectionData(&normal_data);
    std::cout << "正常数据验证结果: " << (is_valid ? "通过" : "失败") << std::endl;
    
    // 清理
    delete[] normal_data.detection_results;
    delete[] normal_data.column_segments;
    
    // 测试2: 模拟垃圾数据
    std::cout << "\n=== 测试2: 垃圾数据 ===" << std::endl;
    InternalDetectionData corrupted_data;
    corrupted_data.num_detections = 1610614992;  // 模拟日志中的异常值
    corrupted_data.num_segments = 1851875192;    // 模拟日志中的异常值
    corrupted_data.detection_results = nullptr;
    corrupted_data.column_segments = nullptr;
    corrupted_data.S_head = nullptr;
    
    is_valid = validateDetectionData(&corrupted_data);
    std::cout << "垃圾数据验证结果: " << (is_valid ? "通过" : "失败") << std::endl;
    
    // 测试3: 不一致的计数
    std::cout << "\n=== 测试3: 不一致计数 ===" << std::endl;
    InternalDetectionData inconsistent_data;
    std::memset(&inconsistent_data, 0, sizeof(InternalDetectionData));
    inconsistent_data.num_detections = 3;
    inconsistent_data.num_segments = 5;  // 不匹配
    inconsistent_data.detection_results = new DetectionResult[3];
    inconsistent_data.column_segments = new ColumnSegmentData[5];
    
    is_valid = validateDetectionData(&inconsistent_data);
    std::cout << "不一致数据验证结果: " << (is_valid ? "通过" : "失败") << std::endl;
    
    // 清理
    delete[] inconsistent_data.detection_results;
    delete[] inconsistent_data.column_segments;
    
    // 测试4: 空指针但计数非零
    std::cout << "\n=== 测试4: 空指针但计数非零 ===" << std::endl;
    InternalDetectionData null_ptr_data;
    std::memset(&null_ptr_data, 0, sizeof(InternalDetectionData));
    null_ptr_data.num_detections = 3;
    null_ptr_data.num_segments = 3;
    null_ptr_data.detection_results = nullptr;  // 空指针但计数非零
    null_ptr_data.column_segments = nullptr;    // 空指针但计数非零
    
    is_valid = validateDetectionData(&null_ptr_data);
    std::cout << "空指针数据验证结果: " << (is_valid ? "通过" : "失败") << std::endl;
    
    // 测试5: 边界值测试
    std::cout << "\n=== 测试5: 边界值测试 ===" << std::endl;
    InternalDetectionData boundary_data;
    std::memset(&boundary_data, 0, sizeof(InternalDetectionData));
    boundary_data.num_detections = 10001;  // 超过最大合理值
    boundary_data.num_segments = 10001;
    boundary_data.detection_results = nullptr;
    boundary_data.column_segments = nullptr;
    
    is_valid = validateDetectionData(&boundary_data);
    std::cout << "边界值数据验证结果: " << (is_valid ? "通过" : "失败") << std::endl;
    
    // 测试6: 测试 ReleaseInternalDetectionData 的安全性
    std::cout << "\n=== 测试6: 安全释放测试 ===" << std::endl;
    InternalDetectionData release_test_data;
    release_test_data.num_detections = 1610614992;  // 垃圾数据
    release_test_data.num_segments = 1851875192;    // 垃圾数据
    release_test_data.detection_results = nullptr;
    release_test_data.column_segments = nullptr;
    release_test_data.S_head = nullptr;
    
    std::cout << "尝试释放包含垃圾数据的结构..." << std::endl;
    try {
        ReleaseInternalDetectionData(&release_test_data);
        std::cout << "安全释放成功" << std::endl;
        std::cout << "释放后 num_detections: " << release_test_data.num_detections << std::endl;
        std::cout << "释放后 num_segments: " << release_test_data.num_segments << std::endl;
    } catch (const std::exception& e) {
        std::cout << "释放时发生异常: " << e.what() << std::endl;
    }
    
    std::cout << "\n所有测试完成!" << std::endl;
    return 0;
}
