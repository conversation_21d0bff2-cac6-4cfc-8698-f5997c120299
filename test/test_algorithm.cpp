#include "libSCR_5000_Alg.hpp"
#include <iostream>
#include <vector>
#include <string>
#include <fstream>
#include <chrono>
#include <cstring>
#include <iomanip>
#include <algorithm>
#include <dirent.h>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <atomic>

// 常量定义
const int ROWS = 1024;
const int COLS = 2048;

// ==================== 工具函数 ====================

// 文件名比较函数：按数字排序（如 file_1.bin < file_2.bin）
bool compareFilesByNumber(const std::string& a, const std::string& b) {
    auto extractBaseName = [](const std::string& path) {
        size_t lastSlash = path.find_last_of("/\\");
        size_t lastDot = path.find_last_of('.');
        if (lastDot == std::string::npos) lastDot = path.size();
        return path.substr(lastSlash + 1, lastDot - lastSlash - 1);
    };
    std::string baseA = extractBaseName(a);
    std::string baseB = extractBaseName(b);
    size_t posA = 0, posB = 0;
    auto getNumber = [](const std::string& s, size_t& pos) {
        long num = 0;
        while (pos < s.size() && isdigit(s[pos])) {
            num = num * 10 + (s[pos] - '0');
            pos++;
        }
        if (pos < s.size() && s[pos] == '_') pos++;
        return num;
    };
    while (posA < baseA.size() && posB < baseB.size()) {
        long numA = getNumber(baseA, posA);
        long numB = getNumber(baseB, posB);
        if (numA != numB) return numA < numB;
    }
    return baseA.size() < baseB.size();
}

// 获取指定目录下的 .bin 文件
std::vector<std::string> get_bin_files(const std::string& folder_path) {
    std::vector<std::string> bin_files;
    DIR* dir = opendir(folder_path.c_str());
    if (!dir) {
        std::cerr << "Cannot open directory: " << folder_path << std::endl;
        return bin_files;
    }
    dirent* ent;
    while ((ent = readdir(dir))) {
        std::string name(ent->d_name);
        if (name.size() > 4 && name.substr(name.size() - 4) == ".bin")
            bin_files.push_back(folder_path + "/" + name);
    }
    closedir(dir);
    std::sort(bin_files.begin(), bin_files.end(), compareFilesByNumber);
    return bin_files;
}

// 从多个文件中加载雷达数据（3帧一组）
struct RawRadarData {
    std::vector<char> bytes;   // 文件原始字节
    char* head_data = nullptr; // 帧头指针
    char* data = nullptr;      // 数据指针
    size_t data_size = 0;      // 数据大小
};

bool LoadRadarDataFromFile(const std::vector<std::string>& file_paths, RawRadarData& out)
{
    if (file_paths.size() != 3) return false;

    constexpr size_t S_head_bytes = 1024 * 40;                     // 单帧头大小
    constexpr size_t per_frame_head = S_head_bytes * 2;            // 双通道头
    constexpr size_t S_data_bytes = 1024 * 2048 * 2 * sizeof(float); // 单帧数据
    constexpr size_t per_frame_data = S_data_bytes * 2;            // 双通道数据

    std::vector<char> headers; headers.reserve(per_frame_head * 3);
    std::vector<char> datas;   datas.reserve(per_frame_data * 3);

    for (const auto& file_path : file_paths) {
        std::ifstream file(file_path, std::ios::binary | std::ios::ate);
        if (!file) return false;
        std::streamsize sz = file.tellg();
        file.seekg(0, std::ios::beg);
        if ((size_t)sz < per_frame_head + per_frame_data) {
            std::cerr << "文件尺寸小于预期: " << file_path << std::endl;
            return false;
        }
        std::vector<char> tmp((size_t)sz);
        if (!file.read(tmp.data(), sz)) return false;
        headers.insert(headers.end(), tmp.data(), tmp.data() + per_frame_head);
        datas.insert(datas.end(), tmp.data() + per_frame_head, tmp.data() + per_frame_head + per_frame_data);
    }
    out.bytes.clear();
    out.bytes.insert(out.bytes.end(), headers.begin(), headers.end());
    out.bytes.insert(out.bytes.end(), datas.begin(), datas.end());

    out.head_data = out.bytes.data();
    out.data      = out.bytes.data() + headers.size();
    out.data_size = datas.size();
    return true;
}

// ==================== 线程安全队列 ====================
template<typename T>
class SafeQueue {
    std::queue<T> q;
    std::mutex m;
    std::condition_variable cv;
public:
    void push(T value) {
        {
            std::lock_guard<std::mutex> lock(m);
            q.push(std::move(value));
        }
        cv.notify_one();
    }
    T pop() {
        std::unique_lock<std::mutex> lock(m);
        cv.wait(lock, [&]{ return !q.empty(); });
        T val = std::move(q.front());
        q.pop();
        return val;
    }
    bool empty() {
        std::lock_guard<std::mutex> lock(m);
        return q.empty();
    }
};

// ==================== 全局队列与标志 ====================
SafeQueue<RawRadarData> radar_queue;
std::atomic<bool> running{true};

// ==================== 打印函数 ====================
void PrintDetectionResults(const DetectionResult* results, int num_results) {
    std::cout << "\n=== 检测结果 ===\n";
    std::cout << "检测到 " << num_results << " 个目标\n";
    for (int i = 0; i < std::min(num_results, 10); ++i) {
        const auto& r = results[i];
        std::cout << "目标" << i+1 << ": row=" << r.row << " col=" << r.col 
                  << " frame=" << r.frame << std::endl;
    }
}

void PrintTrackingResults(const TrackingResult* results, int num_results) {
    std::cout << "\n=== 跟踪结果 ===\n";
    for (int i = 0; i < num_results; ++i) {
        const auto& r = results[i];
        std::cout << "ID=" << r.id << " pos=("<<r.x<<","<<r.y<<","<<r.z
                  <<") v=("<<r.vx<<","<<r.vy<<","<<r.vz<<")\n";
    }
}

// ==================== 生产者线程：每50ms提供一次原始数据 ====================
void Producer(const std::vector<std::string>& bin_files) {
    const size_t window = 3;
    size_t i = 0;
    auto next_time = std::chrono::steady_clock::now();

    while (running) {
        // 循环取 3 个文件
        std::vector<std::string> current_group;
        for (size_t j = 0; j < window; ++j) {
            size_t idx = (i + j) % bin_files.size();
            current_group.push_back(bin_files[idx]);
        }

        RawRadarData raw;
        if (LoadRadarDataFromFile(current_group, raw)) {
            // 打印时间戳（精确到 ms）
            auto now = std::chrono::system_clock::now();
            auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;
            std::time_t t = std::chrono::system_clock::to_time_t(now);
            std::tm tm = *std::localtime(&t);
            std::cout << "[Producer] 提供3帧原始数据时间: "
                      << std::put_time(&tm, "%H:%M:%S") << "."
                      << std::setw(3) << std::setfill('0') << ms.count()
                      << std::endl;

            radar_queue.push(std::move(raw));
        } else {
            std::cerr << "加载数据失败\n";
        }

        i++; // 下一组

        // 等待到下一个 50ms
        next_time += std::chrono::milliseconds(10);
        std::this_thread::sleep_until(next_time);
    }
}

// ==================== 消费者线程：执行检测+跟踪 ====================
void Consumer() {
    while (running || !radar_queue.empty()) {
        RawRadarData raw = radar_queue.pop();

        // 目标检测
        InternalDetectionData detection_data;
        if (TargetDetection(raw.head_data, raw.data, &detection_data) == 0) {
            PrintDetectionResults(detection_data.detection_results, detection_data.num_detections);
            // delete[] raw.head_data;
            // delete[] raw.data;
            // 目标跟踪
            TrackingResult tracking_results[256];
            int num_tracks;
            TargetTracking(&detection_data, tracking_results, &num_tracks);
            PrintTrackingResults(tracking_results, num_tracks);
        }
    }
}

// ==================== 主函数 ====================
int main() {
    AlgorithmVersion version;
    GetVersionInfo(&version);
    std::cout << "算法库版本: " << version.version_string << "\n";

    if (InitializeAlgorithmLibrary("config/custom_config.json") != 0) {
        std::cerr << "算法库初始化失败\n";
        return -1;
    }

    std::string test_data_dir = "/media/llh/新加卷/采集数据/0905_2/data_20250907145439/重排";
    auto bin_files = get_bin_files(test_data_dir);
    if (bin_files.size() < 3) {
        std::cerr << "bin 文件不足3个\n";
        return -1;
    }

    // 启动生产者和消费者
    std::thread producer(Producer, bin_files);
    std::thread consumer(Consumer);

    producer.join();
    consumer.join();

    ReleaseAllResources();
    return 0;
}
