#pragma once

#include <memory>
#include <vector>
#include <string>
#include <functional>
#include <chrono>
#include <mutex>
#include <atomic>
#include <unordered_map>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <filesystem>
#include <spdlog/spdlog.h>

namespace scr5000 {

// ==================== 测试结果枚举 ====================
enum class TestResult {
    PASSED,
    FAILED,
    SKIPPED,
    ERROR
};

// ==================== 测试用例基类 ====================
class TestCase {
private:
    std::string name_;
    std::string description_;
    std::function<TestResult()> test_function_;
    std::chrono::milliseconds timeout_;
    bool enabled_;

public:
    TestCase(const std::string& name, 
             const std::string& description,
             std::function<TestResult()> test_func,
             std::chrono::milliseconds timeout = std::chrono::milliseconds(30000))
        : name_(name), description_(description), test_function_(test_func), 
          timeout_(timeout), enabled_(true) {}

    const std::string& getName() const { return name_; }
    const std::string& getDescription() const { return description_; }
    std::chrono::milliseconds getTimeout() const { return timeout_; }
    bool isEnabled() const { return enabled_; }
    
    void setEnabled(bool enabled) { enabled_ = enabled; }
    
    TestResult execute() {
        if (!enabled_) {
            return TestResult::SKIPPED;
        }
        
        try {
            return test_function_();
        } catch (const std::exception& e) {
            spdlog::error("Exception in test '{}': {}", name_, e.what());
            return TestResult::ERROR;
        } catch (...) {
            spdlog::error("Unknown exception in test '{}'", name_);
            return TestResult::ERROR;
        }
    }
};

// ==================== 测试套件 ====================
class TestSuite {
private:
    std::string name_;
    std::vector<std::unique_ptr<TestCase>> test_cases_;
    std::function<void()> setup_function_;
    std::function<void()> teardown_function_;

public:
    explicit TestSuite(const std::string& name) : name_(name) {}
    
    void setSetup(std::function<void()> setup) { setup_function_ = setup; }
    void setTeardown(std::function<void()> teardown) { teardown_function_ = teardown; }
    
    void addTestCase(std::unique_ptr<TestCase> test_case) {
        test_cases_.push_back(std::move(test_case));
    }
    
    void addTestCase(const std::string& name, 
                     const std::string& description,
                     std::function<TestResult()> test_func,
                     std::chrono::milliseconds timeout = std::chrono::milliseconds(30000)) {
        auto test_case = std::make_unique<TestCase>(name, description, test_func, timeout);
        test_cases_.push_back(std::move(test_case));
    }
    
    const std::string& getName() const { return name_; }
    size_t getTestCount() const { return test_cases_.size(); }
    
    struct SuiteResult {
        std::string suite_name;
        size_t total_tests = 0;
        size_t passed_tests = 0;
        size_t failed_tests = 0;
        size_t skipped_tests = 0;
        size_t error_tests = 0;
        std::chrono::milliseconds total_duration{0};
        std::vector<std::pair<std::string, TestResult>> test_results;
        
        bool isSuccess() const {
            return failed_tests == 0 && error_tests == 0;
        }
        
        double getSuccessRate() const {
            return total_tests > 0 ? static_cast<double>(passed_tests) / total_tests * 100.0 : 0.0;
        }
    };
    
    SuiteResult run() {
        SuiteResult result;
        result.suite_name = name_;
        result.total_tests = test_cases_.size();
        
        spdlog::info("Running test suite: {} ({} tests)", name_, test_cases_.size());
        
        auto suite_start = std::chrono::steady_clock::now();
        
        // 执行setup
        if (setup_function_) {
            try {
                setup_function_();
                spdlog::debug("Test suite setup completed: {}", name_);
            } catch (const std::exception& e) {
                spdlog::error("Test suite setup failed for '{}': {}", name_, e.what());
                // 如果setup失败，跳过所有测试
                for (const auto& test_case : test_cases_) {
                    result.test_results.emplace_back(test_case->getName(), TestResult::SKIPPED);
                    result.skipped_tests++;
                }
                return result;
            }
        }
        
        // 执行测试用例
        for (const auto& test_case : test_cases_) {
            if (!test_case->isEnabled()) {
                result.test_results.emplace_back(test_case->getName(), TestResult::SKIPPED);
                result.skipped_tests++;
                continue;
            }
            
            spdlog::info("Running test: {}", test_case->getName());
            auto test_start = std::chrono::steady_clock::now();
            
            TestResult test_result = test_case->execute();
            
            auto test_end = std::chrono::steady_clock::now();
            auto test_duration = std::chrono::duration_cast<std::chrono::milliseconds>(test_end - test_start);
            
            result.test_results.emplace_back(test_case->getName(), test_result);
            
            switch (test_result) {
                case TestResult::PASSED:
                    result.passed_tests++;
                    spdlog::info("Test PASSED: {} ({}ms)", test_case->getName(), test_duration.count());
                    break;
                case TestResult::FAILED:
                    result.failed_tests++;
                    spdlog::error("Test FAILED: {} ({}ms)", test_case->getName(), test_duration.count());
                    break;
                case TestResult::SKIPPED:
                    result.skipped_tests++;
                    spdlog::info("Test SKIPPED: {}", test_case->getName());
                    break;
                case TestResult::ERROR:
                    result.error_tests++;
                    spdlog::error("Test ERROR: {} ({}ms)", test_case->getName(), test_duration.count());
                    break;
            }
        }
        
        // 执行teardown
        if (teardown_function_) {
            try {
                teardown_function_();
                spdlog::debug("Test suite teardown completed: {}", name_);
            } catch (const std::exception& e) {
                spdlog::error("Test suite teardown failed for '{}': {}", name_, e.what());
            }
        }
        
        auto suite_end = std::chrono::steady_clock::now();
        result.total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(suite_end - suite_start);
        
        spdlog::info("Test suite '{}' completed: {}/{} passed ({:.1f}%) in {}ms",
                    name_, result.passed_tests, result.total_tests, 
                    result.getSuccessRate(), result.total_duration.count());
        
        return result;
    }
};

// ==================== 测试运行器 ====================
class TestRunner {
private:
    std::vector<std::unique_ptr<TestSuite>> test_suites_;
    std::string output_directory_;
    bool verbose_output_;
    std::atomic<bool> stop_on_failure_;

public:
    explicit TestRunner(const std::string& output_dir = "test_results", bool verbose = true)
        : output_directory_(output_dir), verbose_output_(verbose), stop_on_failure_(false) {}
    
    void addTestSuite(std::unique_ptr<TestSuite> suite) {
        test_suites_.push_back(std::move(suite));
    }
    
    void setStopOnFailure(bool stop) { stop_on_failure_.store(stop); }
    void setVerbose(bool verbose) { verbose_output_ = verbose; }
    
    struct RunnerResult {
        size_t total_suites = 0;
        size_t successful_suites = 0;
        size_t total_tests = 0;
        size_t passed_tests = 0;
        size_t failed_tests = 0;
        size_t skipped_tests = 0;
        size_t error_tests = 0;
        std::chrono::milliseconds total_duration{0};
        std::vector<TestSuite::SuiteResult> suite_results;
        
        bool isSuccess() const {
            return failed_tests == 0 && error_tests == 0;
        }
        
        double getOverallSuccessRate() const {
            return total_tests > 0 ? static_cast<double>(passed_tests) / total_tests * 100.0 : 0.0;
        }
    };
    
    RunnerResult runAllTests() {
        RunnerResult result;
        result.total_suites = test_suites_.size();
        
        spdlog::info("Starting test runner with {} test suites", test_suites_.size());
        
        auto runner_start = std::chrono::steady_clock::now();
        
        for (const auto& suite : test_suites_) {
            auto suite_result = suite->run();
            
            result.suite_results.push_back(suite_result);
            result.total_tests += suite_result.total_tests;
            result.passed_tests += suite_result.passed_tests;
            result.failed_tests += suite_result.failed_tests;
            result.skipped_tests += suite_result.skipped_tests;
            result.error_tests += suite_result.error_tests;
            
            if (suite_result.isSuccess()) {
                result.successful_suites++;
            }
            
            // 如果设置了失败时停止，且当前套件失败，则停止执行
            if (stop_on_failure_.load() && !suite_result.isSuccess()) {
                spdlog::warn("Stopping test execution due to suite failure: {}", suite->getName());
                break;
            }
        }
        
        auto runner_end = std::chrono::steady_clock::now();
        result.total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(runner_end - runner_start);
        
        // 生成测试报告
        generateReport(result);
        
        spdlog::info("Test runner completed: {}/{} tests passed ({:.1f}%) in {}ms",
                    result.passed_tests, result.total_tests, 
                    result.getOverallSuccessRate(), result.total_duration.count());
        
        return result;
    }
    
private:
    void generateReport(const RunnerResult& result) {
        try {
            // 创建输出目录
            std::filesystem::create_directories(output_directory_);
            
            // 生成详细报告
            generateDetailedReport(result);
            
            // 生成JUnit XML报告
            generateJUnitReport(result);
            
            // 生成HTML报告
            generateHTMLReport(result);
            
        } catch (const std::exception& e) {
            spdlog::error("Failed to generate test reports: {}", e.what());
        }
    }
    
    void generateDetailedReport(const RunnerResult& result) {
        std::string filename = output_directory_ + "/detailed_report.txt";
        std::ofstream file(filename);
        
        if (!file.is_open()) {
            spdlog::error("Failed to create detailed report file: {}", filename);
            return;
        }
        
        file << "=== SCR-5000 Comprehensive Test Report ===\n";
        file << "Generated: " << getCurrentTimestamp() << "\n\n";
        
        file << "Overall Summary:\n";
        file << "  Total Suites: " << result.total_suites << "\n";
        file << "  Successful Suites: " << result.successful_suites << "\n";
        file << "  Total Tests: " << result.total_tests << "\n";
        file << "  Passed: " << result.passed_tests << "\n";
        file << "  Failed: " << result.failed_tests << "\n";
        file << "  Skipped: " << result.skipped_tests << "\n";
        file << "  Errors: " << result.error_tests << "\n";
        file << "  Success Rate: " << std::fixed << std::setprecision(2) << result.getOverallSuccessRate() << "%\n";
        file << "  Total Duration: " << result.total_duration.count() << "ms\n\n";
        
        for (const auto& suite_result : result.suite_results) {
            file << "Suite: " << suite_result.suite_name << "\n";
            file << "  Tests: " << suite_result.total_tests << "\n";
            file << "  Passed: " << suite_result.passed_tests << "\n";
            file << "  Failed: " << suite_result.failed_tests << "\n";
            file << "  Skipped: " << suite_result.skipped_tests << "\n";
            file << "  Errors: " << suite_result.error_tests << "\n";
            file << "  Success Rate: " << std::fixed << std::setprecision(2) << suite_result.getSuccessRate() << "%\n";
            file << "  Duration: " << suite_result.total_duration.count() << "ms\n";
            
            if (verbose_output_) {
                file << "  Test Results:\n";
                for (const auto& [test_name, test_result] : suite_result.test_results) {
                    std::string result_str;
                    switch (test_result) {
                        case TestResult::PASSED: result_str = "PASSED"; break;
                        case TestResult::FAILED: result_str = "FAILED"; break;
                        case TestResult::SKIPPED: result_str = "SKIPPED"; break;
                        case TestResult::ERROR: result_str = "ERROR"; break;
                    }
                    file << "    " << test_name << ": " << result_str << "\n";
                }
            }
            file << "\n";
        }
        
        spdlog::info("Detailed test report generated: {}", filename);
    }
    
    void generateJUnitReport(const RunnerResult& result) {
        // JUnit XML格式报告实现
        std::string filename = output_directory_ + "/junit_report.xml";
        std::ofstream file(filename);
        
        if (!file.is_open()) {
            spdlog::error("Failed to create JUnit report file: {}", filename);
            return;
        }
        
        file << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
        file << "<testsuites tests=\"" << result.total_tests << "\" "
             << "failures=\"" << result.failed_tests << "\" "
             << "errors=\"" << result.error_tests << "\" "
             << "skipped=\"" << result.skipped_tests << "\" "
             << "time=\"" << result.total_duration.count() / 1000.0 << "\">\n";
        
        for (const auto& suite_result : result.suite_results) {
            file << "  <testsuite name=\"" << suite_result.suite_name << "\" "
                 << "tests=\"" << suite_result.total_tests << "\" "
                 << "failures=\"" << suite_result.failed_tests << "\" "
                 << "errors=\"" << suite_result.error_tests << "\" "
                 << "skipped=\"" << suite_result.skipped_tests << "\" "
                 << "time=\"" << suite_result.total_duration.count() / 1000.0 << "\">\n";
            
            for (const auto& [test_name, test_result] : suite_result.test_results) {
                file << "    <testcase name=\"" << test_name << "\">\n";
                
                switch (test_result) {
                    case TestResult::FAILED:
                        file << "      <failure message=\"Test failed\"/>\n";
                        break;
                    case TestResult::ERROR:
                        file << "      <error message=\"Test error\"/>\n";
                        break;
                    case TestResult::SKIPPED:
                        file << "      <skipped message=\"Test skipped\"/>\n";
                        break;
                    case TestResult::PASSED:
                        // 无需额外标记
                        break;
                }
                
                file << "    </testcase>\n";
            }
            
            file << "  </testsuite>\n";
        }
        
        file << "</testsuites>\n";
        
        spdlog::info("JUnit test report generated: {}", filename);
    }
    
    void generateHTMLReport(const RunnerResult& result) {
        // 简化的HTML报告实现
        std::string filename = output_directory_ + "/report.html";
        std::ofstream file(filename);
        
        if (!file.is_open()) {
            spdlog::error("Failed to create HTML report file: {}", filename);
            return;
        }
        
        file << "<!DOCTYPE html>\n<html>\n<head>\n";
        file << "<title>SCR-5000 Test Report</title>\n";
        file << "<style>\n";
        file << "body { font-family: Arial, sans-serif; margin: 20px; }\n";
        file << ".passed { color: green; }\n";
        file << ".failed { color: red; }\n";
        file << ".skipped { color: orange; }\n";
        file << ".error { color: purple; }\n";
        file << "table { border-collapse: collapse; width: 100%; }\n";
        file << "th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n";
        file << "th { background-color: #f2f2f2; }\n";
        file << "</style>\n</head>\n<body>\n";
        
        file << "<h1>SCR-5000 Test Report</h1>\n";
        file << "<p>Generated: " << getCurrentTimestamp() << "</p>\n";
        
        file << "<h2>Summary</h2>\n";
        file << "<table>\n";
        file << "<tr><th>Metric</th><th>Value</th></tr>\n";
        file << "<tr><td>Total Tests</td><td>" << result.total_tests << "</td></tr>\n";
        file << "<tr><td>Passed</td><td class=\"passed\">" << result.passed_tests << "</td></tr>\n";
        file << "<tr><td>Failed</td><td class=\"failed\">" << result.failed_tests << "</td></tr>\n";
        file << "<tr><td>Skipped</td><td class=\"skipped\">" << result.skipped_tests << "</td></tr>\n";
        file << "<tr><td>Errors</td><td class=\"error\">" << result.error_tests << "</td></tr>\n";
        file << "<tr><td>Success Rate</td><td>" << std::fixed << std::setprecision(2) << result.getOverallSuccessRate() << "%</td></tr>\n";
        file << "<tr><td>Duration</td><td>" << result.total_duration.count() << "ms</td></tr>\n";
        file << "</table>\n";
        
        file << "</body>\n</html>\n";
        
        spdlog::info("HTML test report generated: {}", filename);
    }
    
    std::string getCurrentTimestamp() {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
        return ss.str();
    }
};

// ==================== 全局测试函数声明 ====================
int runComprehensiveTests(const std::string& output_dir = "test_results");

// 扩展测试函数
TestResult testMemoryLeaks();
TestResult testLongTermStability();

// 测试套件创建函数
std::unique_ptr<TestSuite> createBasicTestSuite();
std::unique_ptr<TestSuite> createAdvancedTestSuite();
std::unique_ptr<TestSuite> createStressTestSuite();
std::unique_ptr<TestSuite> createExtendedTestSuite();

} // namespace scr5000
