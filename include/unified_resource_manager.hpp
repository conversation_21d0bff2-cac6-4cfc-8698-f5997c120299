#pragma once

#include <memory>
#include <vector>
#include <atomic>
#include <mutex>
#include "resource_manager.hpp"
#include "memory_pool.hpp"
#include "PointTracker.hpp"
#include "fft_gpu.hpp"
#include "config.hpp"

namespace scr5000 {

// ==================== 统一的资源管理器 ====================
class UnifiedResourceManager {
private:
    // 核心资源
    std::unique_ptr<GPUResourceManager> gpu_manager_;
    std::unique_ptr<MemoryPoolManager> memory_manager_;
    std::unique_ptr<PointTracker> tracker_;
    std::unique_ptr<FFTGPUOptimizer> fft_optimizer_;
    std::unique_ptr<ConfigManager> config_manager_;

    // CPU内存缓冲区
    std::vector<float> output_prob_;
    std::vector<float> sample_input_;

    // 跟踪状态
    std::vector<Point> current_group_detections_;
    int group_start_frame_;
    float prev_azimuth_;
    bool azimuth_unchanged_;

    // 线程安全
    std::atomic<bool> initialized_;
    mutable std::mutex mutex_;

    // 常量
    static constexpr int FRAMES_PER_GROUP = 120;
    static constexpr float INVALID_AZIMUTH = -999.0f;

    // 无锁清理方法
    void cleanupUnlocked_() noexcept;

public:
    UnifiedResourceManager();
    ~UnifiedResourceManager();

    // 禁止拷贝和移动
    UnifiedResourceManager(const UnifiedResourceManager&) = delete;
    UnifiedResourceManager& operator=(const UnifiedResourceManager&) = delete;
    UnifiedResourceManager(UnifiedResourceManager&&) = delete;
    UnifiedResourceManager& operator=(UnifiedResourceManager&&) = delete;

    // 初始化和清理
    bool initialize(const std::string& config_path);
    void cleanup();
    bool isInitialized() const;

    // 资源访问器
    GPUResourceManager* getGPUManager();
    MemoryPoolManager* getMemoryManager();
    PointTracker* getTracker();
    FFTGPUOptimizer* getFFTOptimizer();
    ConfigManager* getConfigManager();

    // 缓冲区访问器
    std::vector<float>& getOutputProb();
    std::vector<float>& getSampleInput();

    // 跟踪状态访问器
    std::vector<Point>& getCurrentGroupDetections();
    int& getGroupStartFrame();
    float& getPrevAzimuth();
    bool& getAzimuthUnchanged();

    // 常量访问器
    int getFramesPerGroup() const;
    float getInvalidAzimuth() const;
};

// ==================== 全局访问函数 ====================
bool initializeResourceManager(const std::string& config_path);
void cleanupResourceManager();
UnifiedResourceManager* getResourceManager();

} // namespace scr5000
