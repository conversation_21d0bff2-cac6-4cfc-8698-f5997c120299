#pragma once

#include <memory>
#include <string>
#include <vector>
#include <chrono>
#include <mutex>
#include <atomic>
#include <unordered_map>
#include <fstream>
#include <iomanip>
#include <sstream>
#include <algorithm>
#include <limits>
#include <spdlog/spdlog.h>
#include <cuda_runtime.h>

namespace scr5000 {

// ==================== 性能计时器 ====================
class PerformanceTimer {
private:
    std::chrono::high_resolution_clock::time_point start_time_;
    std::string name_;
    bool is_running_;

public:
    explicit PerformanceTimer(const std::string& name) 
        : name_(name), is_running_(false) {}
    
    void start() {
        start_time_ = std::chrono::high_resolution_clock::now();
        is_running_ = true;
        spdlog::debug("Timer '{}' started", name_);
    }
    
    double stop() {
        if (!is_running_) {
            spdlog::warn("Timer '{}' was not running", name_);
            return 0.0;
        }
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(
            end_time - start_time_).count();
        
        double ms = duration / 1000.0;
        is_running_ = false;
        
        spdlog::debug("Timer '{}' stopped: {:.3f}ms", name_, ms);
        return ms;
    }
    
    // RAII计时器
    class ScopedTimer {
    private:
        PerformanceTimer& timer_;
        
    public:
        explicit ScopedTimer(PerformanceTimer& timer) : timer_(timer) {
            timer_.start();
        }
        
        ~ScopedTimer() {
            timer_.stop();
        }
    };
    
    ScopedTimer createScopedTimer() {
        return ScopedTimer(*this);
    }
};

// ==================== 内存使用监控器 ====================
class MemoryMonitor {
private:
    struct MemorySnapshot {
        size_t host_memory_used = 0;
        size_t gpu_memory_used = 0;
        size_t gpu_memory_total = 0;
        std::chrono::steady_clock::time_point timestamp;
        std::string context;
    };
    
    std::vector<MemorySnapshot> snapshots_;
    mutable std::mutex mutex_;
    std::atomic<bool> monitoring_enabled_;
    
public:
    MemoryMonitor() : monitoring_enabled_(true) {}
    
    void takeSnapshot(const std::string& context = "") {
        if (!monitoring_enabled_.load()) return;
        
        MemorySnapshot snapshot;
        snapshot.timestamp = std::chrono::steady_clock::now();
        snapshot.context = context;
        
        // 获取GPU内存信息
        size_t free_mem = 0, total_mem = 0;
        cudaError_t status = cudaMemGetInfo(&free_mem, &total_mem);
        if (status == cudaSuccess) {
            snapshot.gpu_memory_used = total_mem - free_mem;
            snapshot.gpu_memory_total = total_mem;
        }
        
        // 获取主机内存信息（简化版本）
        // 这里可以添加更详细的主机内存监控
        
        std::lock_guard<std::mutex> lock(mutex_);
        snapshots_.push_back(snapshot);
        
        // 限制快照数量
        if (snapshots_.size() > 1000) {
            snapshots_.erase(snapshots_.begin());
        }
        
        spdlog::debug("Memory snapshot taken: GPU {}MB/{} MB, Context: '{}'",
                     snapshot.gpu_memory_used / (1024*1024),
                     snapshot.gpu_memory_total / (1024*1024),
                     context);
    }
    
    void logMemoryUsage(const std::string& context = "") {
        takeSnapshot(context);
        
        std::lock_guard<std::mutex> lock(mutex_);
        if (!snapshots_.empty()) {
            const auto& latest = snapshots_.back();
            spdlog::info("Memory Usage [{}]: GPU {}MB/{}MB ({:.1f}%)",
                        context,
                        latest.gpu_memory_used / (1024*1024),
                        latest.gpu_memory_total / (1024*1024),
                        latest.gpu_memory_total > 0 ? 
                            static_cast<double>(latest.gpu_memory_used) / latest.gpu_memory_total * 100.0 : 0.0);
        }
    }
    
    void exportToCSV(const std::string& filename) const {
        std::lock_guard<std::mutex> lock(mutex_);
        
        std::ofstream file(filename);
        if (!file.is_open()) {
            spdlog::error("Failed to open file for memory export: {}", filename);
            return;
        }
        
        file << "Timestamp,Context,GPU_Used_MB,GPU_Total_MB,GPU_Usage_Percent\n";
        
        for (const auto& snapshot : snapshots_) {
            auto time_since_epoch = snapshot.timestamp.time_since_epoch();
            auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(time_since_epoch).count();
            
            double usage_percent = snapshot.gpu_memory_total > 0 ?
                static_cast<double>(snapshot.gpu_memory_used) / snapshot.gpu_memory_total * 100.0 : 0.0;
            
            file << ms << ","
                 << "\"" << snapshot.context << "\","
                 << snapshot.gpu_memory_used / (1024*1024) << ","
                 << snapshot.gpu_memory_total / (1024*1024) << ","
                 << std::fixed << std::setprecision(2) << usage_percent << "\n";
        }
        
        spdlog::info("Memory usage data exported to: {}", filename);
    }
    
    void enable() { monitoring_enabled_.store(true); }
    void disable() { monitoring_enabled_.store(false); }
    bool isEnabled() const { return monitoring_enabled_.load(); }
    
    size_t getSnapshotCount() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return snapshots_.size();
    }
    
    void clear() {
        std::lock_guard<std::mutex> lock(mutex_);
        snapshots_.clear();
        spdlog::info("Memory monitor snapshots cleared");
    }

    // 增强功能
    void logDetailedMemoryInfo();
    std::string getMemoryTrend() const;
};

// ==================== 性能分析器 ====================
class PerformanceProfiler {
private:
    struct ProfileData {
        std::string name;
        double total_time_ms = 0.0;
        double min_time_ms = std::numeric_limits<double>::max();
        double max_time_ms = 0.0;
        size_t call_count = 0;
        
        double getAverageTime() const {
            return call_count > 0 ? total_time_ms / call_count : 0.0;
        }
    };
    
    std::unordered_map<std::string, ProfileData> profiles_;
    mutable std::mutex mutex_;
    std::atomic<bool> profiling_enabled_;
    
public:
    PerformanceProfiler() : profiling_enabled_(true) {}
    
    void recordTime(const std::string& name, double time_ms) {
        if (!profiling_enabled_.load()) return;
        
        std::lock_guard<std::mutex> lock(mutex_);
        auto& profile = profiles_[name];
        profile.name = name;
        profile.total_time_ms += time_ms;
        profile.min_time_ms = std::min(profile.min_time_ms, time_ms);
        profile.max_time_ms = std::max(profile.max_time_ms, time_ms);
        profile.call_count++;
        
        spdlog::debug("Profile '{}': {:.3f}ms (avg: {:.3f}ms, calls: {})",
                     name, time_ms, profile.getAverageTime(), profile.call_count);
    }
    
    void logSummary() const {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (profiles_.empty()) {
            spdlog::info("No profiling data available");
            return;
        }
        
        spdlog::info("=== Performance Profile Summary ===");
        for (const auto& [name, profile] : profiles_) {
            spdlog::info("  {}: avg={:.3f}ms, min={:.3f}ms, max={:.3f}ms, calls={}, total={:.3f}ms",
                        name, profile.getAverageTime(), profile.min_time_ms, 
                        profile.max_time_ms, profile.call_count, profile.total_time_ms);
        }
    }
    
    void exportToCSV(const std::string& filename) const {
        std::lock_guard<std::mutex> lock(mutex_);
        
        std::ofstream file(filename);
        if (!file.is_open()) {
            spdlog::error("Failed to open file for profile export: {}", filename);
            return;
        }
        
        file << "Name,Average_ms,Min_ms,Max_ms,Call_Count,Total_ms\n";
        
        for (const auto& [name, profile] : profiles_) {
            file << "\"" << name << "\","
                 << std::fixed << std::setprecision(3)
                 << profile.getAverageTime() << ","
                 << profile.min_time_ms << ","
                 << profile.max_time_ms << ","
                 << profile.call_count << ","
                 << profile.total_time_ms << "\n";
        }
        
        spdlog::info("Performance profile data exported to: {}", filename);
    }
    
    void clear() {
        std::lock_guard<std::mutex> lock(mutex_);
        profiles_.clear();
        spdlog::info("Performance profiles cleared");
    }
    
    void enable() { profiling_enabled_.store(true); }
    void disable() { profiling_enabled_.store(false); }
    bool isEnabled() const { return profiling_enabled_.load(); }

    // 增强功能
    void logTopPerformers(size_t top_n = 10) const;
    double getTotalTime() const;
    size_t getTotalCalls() const;
};

// ==================== 调试工具管理器 ====================
class DebugToolsManager {
private:
    std::unique_ptr<MemoryMonitor> memory_monitor_;
    std::unique_ptr<PerformanceProfiler> profiler_;
    std::unordered_map<std::string, std::unique_ptr<PerformanceTimer>> timers_;
    mutable std::mutex mutex_;
    
    static std::unique_ptr<DebugToolsManager> instance_;
    static std::mutex instance_mutex_;
    
    DebugToolsManager() {
        memory_monitor_ = std::make_unique<MemoryMonitor>();
        profiler_ = std::make_unique<PerformanceProfiler>();
    }
    
public:
    static DebugToolsManager* getInstance() {
        std::lock_guard<std::mutex> lock(instance_mutex_);
        if (!instance_) {
            instance_ = std::unique_ptr<DebugToolsManager>(new DebugToolsManager());
        }
        return instance_.get();
    }
    
    static void releaseInstance() {
        std::lock_guard<std::mutex> lock(instance_mutex_);
        instance_.reset();
    }
    
    MemoryMonitor* getMemoryMonitor() { return memory_monitor_.get(); }
    PerformanceProfiler* getProfiler() { return profiler_.get(); }
    
    PerformanceTimer* getOrCreateTimer(const std::string& name) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        auto it = timers_.find(name);
        if (it == timers_.end()) {
            timers_[name] = std::make_unique<PerformanceTimer>(name);
        }
        
        return timers_[name].get();
    }
    
    void exportAllData(const std::string& base_filename) {
        auto timestamp = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
        
        std::string memory_file = base_filename + "_memory_" + std::to_string(timestamp) + ".csv";
        std::string profile_file = base_filename + "_profile_" + std::to_string(timestamp) + ".csv";
        
        memory_monitor_->exportToCSV(memory_file);
        profiler_->exportToCSV(profile_file);
        
        spdlog::info("Debug data exported with timestamp: {}", timestamp);
    }
    
    void logSystemStatus() {
        memory_monitor_->logMemoryUsage("System Status Check");
        profiler_->logSummary();

        std::lock_guard<std::mutex> lock(mutex_);
        spdlog::info("Active timers: {}", timers_.size());
    }

    // 增强功能
    void runDiagnostics();
    void enableAllMonitoring();
    void disableAllMonitoring();
    void clearAllData();
    std::string getStatusSummary();
};

// ==================== 便利宏定义 ====================
#define DEBUG_MEMORY_SNAPSHOT(context) \
    do { \
        auto* monitor = scr5000::DebugToolsManager::getInstance()->getMemoryMonitor(); \
        if (monitor) monitor->takeSnapshot(context); \
    } while(0)

#define DEBUG_TIMER_START(name) \
    auto* timer_##name = scr5000::DebugToolsManager::getInstance()->getOrCreateTimer(#name); \
    timer_##name->start()

#define DEBUG_TIMER_STOP(name) \
    do { \
        auto* timer_##name = scr5000::DebugToolsManager::getInstance()->getOrCreateTimer(#name); \
        double time_ms = timer_##name->stop(); \
        auto* profiler = scr5000::DebugToolsManager::getInstance()->getProfiler(); \
        if (profiler) profiler->recordTime(#name, time_ms); \
    } while(0)

#define DEBUG_SCOPED_TIMER(name) \
    auto* scoped_timer_##name = scr5000::DebugToolsManager::getInstance()->getOrCreateTimer(#name); \
    auto scoped_guard_##name = scoped_timer_##name->createScopedTimer(); \
    auto* profiler_##name = scr5000::DebugToolsManager::getInstance()->getProfiler()

} // namespace scr5000
