#pragma once

#include <vector>
#include <complex>
#include <memory>
#include <string>
#include <array>
#include <cuda_runtime.h>
#include <cufft.h>
#include <spdlog/spdlog.h>
#include "resource_manager.hpp"

struct ColumnSegment {
    std::vector<cufftComplex> data;   // 列数据
    int segment_length;               // 本段实际行数
    int segment_start;                // 全局起始行号
};

class FFTGPUOptimizer {
public:
    FFTGPUOptimizer(int rows, int cols);
    FFTGPUOptimizer(int rows, int cols, scr5000::GPUResourceManager* gpu_manager);
    ~FFTGPUOptimizer();

    // 禁止拷贝和移动
    FFTGPUOptimizer(const FFTGPUOptimizer&) = delete;
    FFTGPUOptimizer& operator=(const FFTGPUOptimizer&) = delete;
    FFTGPUOptimizer(FFTGPUOptimizer&&) = delete;
    FFTGPUOptimizer& operator=(FFTGPUOptimizer&&) = delete;

    // 维度访问
    int getRows() const { return ROWS; }
    int getCols() const { return COLS; }

    // 对单个列数据段执行FFT
    std::complex<float> performFFTOnSegment(
        const std::vector<cufftComplex>& segment_data,
        int target_row,
        int segment_start);

    // 获取资源信息
    std::string getResourceInfo() const;

private:
    int ROWS;
    int COLS;

    // 使用新的资源管理器
    scr5000::GPUResourceManager* gpu_manager_;

    // 预定义的4档长度
    static constexpr std::array<int, 4> SEGMENT_LENGTHS = {512, 256, 128, 64};
    static constexpr int BUFFERS_PER_LENGTH = 10;

    // 资源池结构
    struct FFTResourcePool {
        std::array<std::unique_ptr<scr5000::CudaMemoryRAII<cufftComplex>>, BUFFERS_PER_LENGTH> buffers;
        std::array<std::unique_ptr<scr5000::CuFFTPlanRAII>, BUFFERS_PER_LENGTH> plans;
        std::array<bool, BUFFERS_PER_LENGTH> buffer_in_use;
        int length;

        FFTResourcePool() : buffer_in_use{false}, length(0) {}
    };

    // 4档长度的资源池
    std::array<FFTResourcePool, 4> resource_pools_;

    // 初始化所有资源池
    bool initializeResourcePools();

    // 获取指定长度的可用资源
    std::pair<scr5000::CudaMemoryRAII<cufftComplex>*, scr5000::CuFFTPlanRAII*>
    acquireResources(int segment_length);

    // 释放资源
    void releaseResources(scr5000::CudaMemoryRAII<cufftComplex>* buffer);

    // 找到最接近的段长度索引
    int findClosestLengthIndex(int segment_length) const;

    // 清理资源
    void cleanup();
};
