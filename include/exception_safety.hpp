#pragma once

#include <memory>
#include <string>
#include <vector>
#include <functional>
#include <exception>
#include <stdexcept>
#include <mutex>
#include <atomic>
#include <chrono>
#include <spdlog/spdlog.h>
#include <cuda_runtime.h>

namespace scr5000 {

// ==================== 自定义异常类型 ====================
class SCRException : public std::runtime_error {
private:
    std::string error_code_;
    std::string context_;
    std::chrono::steady_clock::time_point timestamp_;

public:
    SCRException(const std::string& message, 
                 const std::string& error_code = "UNKNOWN", 
                 const std::string& context = "")
        : std::runtime_error(message), error_code_(error_code), context_(context),
          timestamp_(std::chrono::steady_clock::now()) {}

    const std::string& getErrorCode() const { return error_code_; }
    const std::string& getContext() const { return context_; }
    std::chrono::steady_clock::time_point getTimestamp() const { return timestamp_; }
    
    std::string getDetailedMessage() const {
        auto time_since_epoch = timestamp_.time_since_epoch();
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(time_since_epoch).count();
        
        return fmt::format("[{}] {} (Code: {}, Context: {})", 
                          ms, what(), error_code_, context_);
    }
};

class CudaException : public SCRException {
private:
    cudaError_t cuda_error_;

public:
    CudaException(cudaError_t error, const std::string& context = "")
        : SCRException(fmt::format("CUDA Error: {}", cudaGetErrorString(error)), 
                      fmt::format("CUDA_{}", static_cast<int>(error)), context),
          cuda_error_(error) {}

    cudaError_t getCudaError() const { return cuda_error_; }
};

class TensorRTException : public SCRException {
public:
    TensorRTException(const std::string& message, const std::string& context = "")
        : SCRException(message, "TENSORRT_ERROR", context) {}
};

class MemoryException : public SCRException {
private:
    size_t requested_size_;

public:
    MemoryException(const std::string& message, size_t requested_size = 0, const std::string& context = "")
        : SCRException(message, "MEMORY_ERROR", context), requested_size_(requested_size) {}

    size_t getRequestedSize() const { return requested_size_; }
};

class InitializationException : public SCRException {
public:
    InitializationException(const std::string& message, const std::string& context = "")
        : SCRException(message, "INIT_ERROR", context) {}
};

// ==================== RAII资源守护者 ====================
template<typename T>
class ResourceGuard {
private:
    T resource_;
    std::function<void(T&)> cleanup_func_;
    bool is_valid_;

public:
    template<typename CleanupFunc>
    ResourceGuard(T resource, CleanupFunc cleanup) 
        : resource_(std::move(resource)), cleanup_func_(cleanup), is_valid_(true) {}

    ~ResourceGuard() {
        if (is_valid_) {
            try {
                cleanup_func_(resource_);
            } catch (const std::exception& e) {
                spdlog::error("Exception during resource cleanup: {}", e.what());
            } catch (...) {
                spdlog::error("Unknown exception during resource cleanup");
            }
        }
    }

    // 禁止拷贝
    ResourceGuard(const ResourceGuard&) = delete;
    ResourceGuard& operator=(const ResourceGuard&) = delete;

    // 允许移动
    ResourceGuard(ResourceGuard&& other) noexcept 
        : resource_(std::move(other.resource_)), 
          cleanup_func_(std::move(other.cleanup_func_)),
          is_valid_(other.is_valid_) {
        other.is_valid_ = false;
    }

    ResourceGuard& operator=(ResourceGuard&& other) noexcept {
        if (this != &other) {
            if (is_valid_) {
                cleanup_func_(resource_);
            }
            resource_ = std::move(other.resource_);
            cleanup_func_ = std::move(other.cleanup_func_);
            is_valid_ = other.is_valid_;
            other.is_valid_ = false;
        }
        return *this;
    }

    T& get() { return resource_; }
    const T& get() const { return resource_; }
    
    T* operator->() { return &resource_; }
    const T* operator->() const { return &resource_; }
    
    T& operator*() { return resource_; }
    const T& operator*() const { return resource_; }

    void release() { is_valid_ = false; }
    bool isValid() const { return is_valid_; }
};

// ==================== 异常安全的操作包装器 ====================
class SafeOperationWrapper {
private:
    std::vector<std::function<void()>> rollback_actions_;
    bool committed_;

public:
    SafeOperationWrapper() : committed_(false) {}

    ~SafeOperationWrapper() {
        if (!committed_) {
            rollback();
        }
    }

    template<typename Action, typename Rollback>
    void addOperation(Action action, Rollback rollback) {
        try {
            action();
            rollback_actions_.push_back(rollback);
        } catch (...) {
            rollback();
            throw;
        }
    }

    void commit() {
        committed_ = true;
        rollback_actions_.clear();
    }

    void rollback() {
        for (auto it = rollback_actions_.rbegin(); it != rollback_actions_.rend(); ++it) {
            try {
                (*it)();
            } catch (const std::exception& e) {
                spdlog::error("Exception during rollback: {}", e.what());
            } catch (...) {
                spdlog::error("Unknown exception during rollback");
            }
        }
        rollback_actions_.clear();
    }
};

// ==================== 错误恢复管理器 ====================
class ErrorRecoveryManager {
private:
    struct RecoveryAction {
        std::string name;
        std::function<bool()> action;
        int priority;
        int max_attempts;
        std::chrono::milliseconds delay;
    };

    std::vector<RecoveryAction> recovery_actions_;
    mutable std::mutex mutex_;
    std::atomic<bool> recovery_in_progress_;

public:
    ErrorRecoveryManager() : recovery_in_progress_(false) {}

    void registerRecoveryAction(const std::string& name,
                               std::function<bool()> action,
                               int priority = 0,
                               int max_attempts = 3,
                               std::chrono::milliseconds delay = std::chrono::milliseconds(1000)) {
        std::lock_guard<std::mutex> lock(mutex_);
        recovery_actions_.push_back({name, action, priority, max_attempts, delay});
        
        // 按优先级排序（高优先级先执行）
        std::sort(recovery_actions_.begin(), recovery_actions_.end(),
                  [](const RecoveryAction& a, const RecoveryAction& b) {
                      return a.priority > b.priority;
                  });
        
        spdlog::debug("Registered recovery action: {} (priority: {})", name, priority);
    }

    bool attemptRecovery(const std::string& error_context = "") {
        if (recovery_in_progress_.exchange(true)) {
            spdlog::warn("Recovery already in progress, skipping");
            return false;
        }

        bool recovery_successful = false;
        
        try {
            spdlog::info("Starting error recovery process for: {}", error_context);
            
            std::lock_guard<std::mutex> lock(mutex_);
            
            for (const auto& recovery : recovery_actions_) {
                spdlog::info("Attempting recovery action: {}", recovery.name);
                
                bool action_successful = false;
                for (int attempt = 1; attempt <= recovery.max_attempts; ++attempt) {
                    try {
                        if (recovery.action()) {
                            spdlog::info("Recovery action '{}' succeeded on attempt {}", 
                                        recovery.name, attempt);
                            action_successful = true;
                            break;
                        } else {
                            spdlog::warn("Recovery action '{}' failed on attempt {}", 
                                        recovery.name, attempt);
                        }
                    } catch (const std::exception& e) {
                        spdlog::error("Exception in recovery action '{}' attempt {}: {}", 
                                     recovery.name, attempt, e.what());
                    }
                    
                    if (attempt < recovery.max_attempts) {
                        std::this_thread::sleep_for(recovery.delay);
                    }
                }
                
                if (action_successful) {
                    recovery_successful = true;
                    break; // 一个恢复动作成功就足够了
                }
            }
            
            if (recovery_successful) {
                spdlog::info("Error recovery completed successfully");
            } else {
                spdlog::error("All recovery actions failed");
            }
            
        } catch (const std::exception& e) {
            spdlog::error("Exception during error recovery: {}", e.what());
        }
        
        recovery_in_progress_.store(false);
        return recovery_successful;
    }

    void clearRecoveryActions() {
        std::lock_guard<std::mutex> lock(mutex_);
        recovery_actions_.clear();
        spdlog::info("All recovery actions cleared");
    }

    bool isRecoveryInProgress() const {
        return recovery_in_progress_.load();
    }

    size_t getRecoveryActionCount() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return recovery_actions_.size();
    }
};

// ==================== 异常安全的函数执行器 ====================
template<typename Func, typename... Args>
auto safeExecute(const std::string& operation_name, Func&& func, Args&&... args) 
    -> decltype(func(args...)) {
    
    try {
        spdlog::debug("Executing operation: {}", operation_name);
        auto result = func(std::forward<Args>(args)...);
        spdlog::debug("Operation '{}' completed successfully", operation_name);
        return result;
        
    } catch (const SCRException& e) {
        spdlog::error("SCR Exception in operation '{}': {}", operation_name, e.getDetailedMessage());
        throw;
        
    } catch (const std::exception& e) {
        spdlog::error("Standard exception in operation '{}': {}", operation_name, e.what());
        throw SCRException(e.what(), "STD_EXCEPTION", operation_name);
        
    } catch (...) {
        spdlog::error("Unknown exception in operation '{}'", operation_name);
        throw SCRException("Unknown exception occurred", "UNKNOWN_EXCEPTION", operation_name);
    }
}

// ==================== CUDA错误检查宏 ====================
#define CUDA_CHECK_THROW(call) \
    do { \
        cudaError_t error = call; \
        if (error != cudaSuccess) { \
            throw scr5000::CudaException(error, #call); \
        } \
    } while(0)

#define CUDA_CHECK_SAFE(call, context) \
    do { \
        cudaError_t error = call; \
        if (error != cudaSuccess) { \
            spdlog::error("CUDA error in {}: {} ({})", context, cudaGetErrorString(error), #call); \
            return false; \
        } \
    } while(0)

// ==================== 异常安全的初始化宏 ====================
#define SAFE_INIT_OPERATION(name, operation) \
    scr5000::safeExecute(name, [&]() { \
        return operation; \
    })

#define SAFE_INIT_WITH_ROLLBACK(name, operation, rollback) \
    do { \
        try { \
            operation; \
        } catch (...) { \
            try { \
                rollback; \
            } catch (const std::exception& e) { \
                spdlog::error("Exception during rollback in {}: {}", name, e.what()); \
            } \
            throw; \
        } \
    } while(0)

// ==================== 全局函数声明 ====================
ErrorRecoveryManager* getGlobalRecoveryManager();
void releaseGlobalRecoveryManager();
void registerDefaultRecoveryActions();

// 异常处理工具函数
void logException(const std::exception& e, const std::string& context = "");
bool handleExceptionWithRecovery(const std::exception& e, const std::string& context = "");

// 异常安全的CUDA操作
bool safeCudaMalloc(void** ptr, size_t size, const std::string& context = "");
bool safeCudaFree(void* ptr, const std::string& context = "");
bool safeCudaMemcpy(void* dst, const void* src, size_t count, cudaMemcpyKind kind, const std::string& context = "");

// 异常安全的资源创建
std::unique_ptr<void, std::function<void(void*)>> makeSafeCudaMemory(size_t size, const std::string& context = "");

// 全局异常处理器设置
void setupGlobalExceptionHandler();

} // namespace scr5000
