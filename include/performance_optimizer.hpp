#pragma once

#include <memory>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <shared_mutex>
#include <atomic>
#include <chrono>
#include <thread>
#include <queue>
#include <condition_variable>
#include <functional>
#include <cstring>
#include <cuda_runtime.h>
#include <spdlog/spdlog.h>

namespace scr5000 {

// ==================== 高性能内存池 ====================
template<typename T>
class HighPerformanceMemoryPool {
private:
    struct MemoryBlock {
        T* ptr;
        size_t size;
        bool is_free;
        std::chrono::steady_clock::time_point last_used;
        size_t allocation_id;
        
        MemoryBlock(T* p, size_t s, size_t id) 
            : ptr(p), size(s), is_free(true), 
              last_used(std::chrono::steady_clock::now()), allocation_id(id) {}
    };
    
    std::vector<std::unique_ptr<MemoryBlock>> blocks_;
    std::unordered_map<T*, size_t> ptr_to_index_;
    mutable std::shared_mutex mutex_;
    
protected:
    size_t total_allocated_;
    size_t total_used_;
    size_t max_pool_size_;
    size_t block_alignment_;
    size_t next_allocation_id_;
    
    // 性能统计
    std::atomic<size_t> allocation_count_;
    std::atomic<size_t> deallocation_count_;
    std::atomic<size_t> cache_hits_;
    std::atomic<size_t> cache_misses_;
    
    // 预分配策略
    std::vector<size_t> common_sizes_;
    size_t prealloc_count_per_size_;
    
    static constexpr size_t DEFAULT_ALIGNMENT = 256;
    static constexpr size_t DEFAULT_PREALLOC_COUNT = 4;

public:
    explicit HighPerformanceMemoryPool(size_t max_size = 1024 * 1024 * 1024, // 1GB
                                      size_t alignment = DEFAULT_ALIGNMENT)
        : total_allocated_(0), total_used_(0), max_pool_size_(max_size),
          block_alignment_(alignment), next_allocation_id_(1),
          allocation_count_(0), deallocation_count_(0), 
          cache_hits_(0), cache_misses_(0),
          prealloc_count_per_size_(DEFAULT_PREALLOC_COUNT) {
        
        // 设置常见的内存块大小
        common_sizes_ = {
            1024,           // 1KB
            4096,           // 4KB
            16384,          // 16KB
            65536,          // 64KB
            262144,         // 256KB
            1048576,        // 1MB
            4194304,        // 4MB
            16777216        // 16MB
        };
        
        spdlog::info("High-performance memory pool created: max_size={}MB, alignment={}",
                    max_size / (1024*1024), alignment);
    }
    
    ~HighPerformanceMemoryPool() {
        cleanup();
    }
    
    // 预分配常用大小的内存块
    void preallocateCommonSizes() {
        std::unique_lock<std::shared_mutex> lock(mutex_);
        
        for (size_t size : common_sizes_) {
            for (size_t i = 0; i < prealloc_count_per_size_; ++i) {
                if (total_allocated_ + size > max_pool_size_) {
                    spdlog::warn("Reached pool size limit during preallocation");
                    return;
                }
                
                T* ptr = allocateRaw(size);
                if (ptr) {
                    auto block = std::make_unique<MemoryBlock>(ptr, size, next_allocation_id_++);
                    ptr_to_index_[ptr] = blocks_.size();
                    blocks_.push_back(std::move(block));
                    total_allocated_ += size;
                    
                    spdlog::debug("Preallocated block: {} elements ({}KB)", size, size * sizeof(T) / 1024);
                }
            }
        }
        
        spdlog::info("Preallocation completed: {} blocks, {}MB total",
                    blocks_.size(), total_allocated_ * sizeof(T) / (1024*1024));
    }
    
    T* allocate(size_t count) {
        size_t aligned_count = alignSize(count);
        
        // 首先尝试快速路径（读锁）
        {
            std::shared_lock<std::shared_mutex> lock(mutex_);
            
            for (auto& block : blocks_) {
                if (block->is_free && block->size >= aligned_count) {
                    // 找到合适的块
                    block->is_free = false;
                    block->last_used = std::chrono::steady_clock::now();
                    total_used_ += block->size;
                    
                    cache_hits_.fetch_add(1);
                    allocation_count_.fetch_add(1);
                    
                    spdlog::debug("Memory pool hit: {} elements from block {}", 
                                 aligned_count, block->allocation_id);
                    return block->ptr;
                }
            }
        }
        
        // 快速路径失败，使用慢速路径（写锁）
        cache_misses_.fetch_add(1);
        return allocateNewBlock(aligned_count);
    }
    
    void deallocate(T* ptr) {
        if (!ptr) return;
        
        std::unique_lock<std::shared_mutex> lock(mutex_);
        
        auto it = ptr_to_index_.find(ptr);
        if (it == ptr_to_index_.end()) {
            spdlog::warn("Attempt to deallocate unknown pointer: {}", static_cast<void*>(ptr));
            return;
        }
        
        size_t index = it->second;
        if (index >= blocks_.size()) {
            spdlog::error("Invalid block index: {}", index);
            return;
        }
        
        auto& block = blocks_[index];
        if (block->is_free) {
            spdlog::warn("Double free detected for block {}", block->allocation_id);
            return;
        }
        
        block->is_free = true;
        block->last_used = std::chrono::steady_clock::now();
        total_used_ -= block->size;
        
        deallocation_count_.fetch_add(1);
        
        spdlog::debug("Memory pool deallocate: block {} freed", block->allocation_id);
    }
    
    // 性能统计
    struct PoolStatistics {
        size_t total_allocated_bytes;
        size_t total_used_bytes;
        size_t num_blocks;
        size_t num_free_blocks;
        double utilization_ratio;
        size_t allocation_count;
        size_t deallocation_count;
        size_t cache_hits;
        size_t cache_misses;
        double cache_hit_ratio;
    };
    
    PoolStatistics getStatistics() const {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        
        size_t free_blocks = 0;
        for (const auto& block : blocks_) {
            if (block->is_free) {
                free_blocks++;
            }
        }
        
        size_t total_allocs = allocation_count_.load();
        size_t hits = cache_hits_.load();
        
        return {
            total_allocated_ * sizeof(T),
            total_used_ * sizeof(T),
            blocks_.size(),
            free_blocks,
            total_allocated_ > 0 ? static_cast<double>(total_used_) / total_allocated_ : 0.0,
            allocation_count_.load(),
            deallocation_count_.load(),
            hits,
            cache_misses_.load(),
            total_allocs > 0 ? static_cast<double>(hits) / total_allocs : 0.0
        };
    }
    
    void cleanup() {
        std::unique_lock<std::shared_mutex> lock(mutex_);
        
        spdlog::info("Cleaning up high-performance memory pool: {} blocks", blocks_.size());
        
        for (auto& block : blocks_) {
            if (block->ptr) {
                deallocateRaw(block->ptr);
            }
        }
        
        blocks_.clear();
        ptr_to_index_.clear();
        total_allocated_ = 0;
        total_used_ = 0;
    }
    
    // 内存整理
    void defragment() {
        std::unique_lock<std::shared_mutex> lock(mutex_);
        
        spdlog::info("Starting memory pool defragmentation...");
        
        // 移除长时间未使用的空闲块
        auto now = std::chrono::steady_clock::now();
        auto threshold = std::chrono::minutes(10);
        
        size_t removed_count = 0;
        size_t removed_bytes = 0;
        
        auto it = blocks_.begin();
        while (it != blocks_.end()) {
            auto& block = *it;
            if (block->is_free && (now - block->last_used) > threshold) {
                // 移除这个块
                ptr_to_index_.erase(block->ptr);
                deallocateRaw(block->ptr);
                
                removed_bytes += block->size * sizeof(T);
                total_allocated_ -= block->size;
                removed_count++;
                
                // 更新索引映射
                size_t removed_index = std::distance(blocks_.begin(), it);
                for (auto& pair : ptr_to_index_) {
                    if (pair.second > removed_index) {
                        pair.second--;
                    }
                }
                
                it = blocks_.erase(it);
            } else {
                ++it;
            }
        }
        
        spdlog::info("Defragmentation completed: removed {} blocks, freed {}MB",
                    removed_count, removed_bytes / (1024*1024));
    }

private:
    size_t alignSize(size_t size) const {
        return (size + block_alignment_ - 1) & ~(block_alignment_ - 1);
    }
    
    virtual T* allocateRaw(size_t count) = 0;
    virtual void deallocateRaw(T* ptr) = 0;
    
    T* allocateNewBlock(size_t count) {
        std::unique_lock<std::shared_mutex> lock(mutex_);
        
        if (total_allocated_ + count > max_pool_size_) {
            spdlog::warn("Pool size limit reached, attempting defragmentation");
            lock.unlock();
            defragment();
            lock.lock();
            
            if (total_allocated_ + count > max_pool_size_) {
                spdlog::error("Cannot allocate {} elements: pool size limit exceeded", count);
                return nullptr;
            }
        }
        
        T* ptr = allocateRaw(count);
        if (!ptr) {
            spdlog::error("Raw allocation failed for {} elements", count);
            return nullptr;
        }
        
        auto block = std::make_unique<MemoryBlock>(ptr, count, next_allocation_id_++);
        block->is_free = false;
        total_allocated_ += count;
        total_used_ += count;
        
        ptr_to_index_[ptr] = blocks_.size();
        blocks_.push_back(std::move(block));
        
        allocation_count_.fetch_add(1);
        
        spdlog::debug("New block allocated: {} elements ({}KB), block ID {}",
                     count, count * sizeof(T) / 1024, next_allocation_id_ - 1);
        
        return ptr;
    }
};

// ==================== CUDA内存池特化 ====================
template<typename T>
class CudaHighPerformancePool : public HighPerformanceMemoryPool<T> {
protected:
    T* allocateRaw(size_t count) override {
        T* ptr = nullptr;
        cudaError_t status = cudaMalloc(reinterpret_cast<void**>(&ptr), count * sizeof(T));
        if (status != cudaSuccess) {
            spdlog::error("CUDA malloc failed: {}", cudaGetErrorString(status));
            return nullptr;
        }
        return ptr;
    }
    
    void deallocateRaw(T* ptr) override {
        if (ptr) {
            cudaError_t status = cudaFree(ptr);
            if (status != cudaSuccess) {
                spdlog::warn("CUDA free failed: {}", cudaGetErrorString(status));
            }
        }
    }

public:
    explicit CudaHighPerformancePool(size_t max_size = 512 * 1024 * 1024) // 512MB
        : HighPerformanceMemoryPool<T>(max_size) {
        spdlog::info("CUDA high-performance memory pool created");
    }
};

// ==================== 主机内存池特化 ====================
template<typename T>
class HostHighPerformancePool : public HighPerformanceMemoryPool<T> {
protected:
    T* allocateRaw(size_t count) override {
        T* ptr = static_cast<T*>(std::aligned_alloc(this->block_alignment_, count * sizeof(T)));
        if (ptr) {
            std::memset(ptr, 0, count * sizeof(T));
        }
        return ptr;
    }
    
    void deallocateRaw(T* ptr) override {
        if (ptr) {
            std::free(ptr);
        }
    }

public:
    explicit HostHighPerformancePool(size_t max_size = 1024 * 1024 * 1024) // 1GB
        : HighPerformanceMemoryPool<T>(max_size) {
        spdlog::info("Host high-performance memory pool created");
    }
};

// ==================== 全局接口函数声明 ====================
bool initializePerformanceOptimization();
void cleanupPerformanceOptimization();
bool isPerformanceOptimizationInitialized();
void logPerformanceStatistics();
void defragmentMemoryPools();

// 便利的分配函数
float* allocateOptimizedCudaFloat(size_t count);
void deallocateOptimizedCudaFloat(float* ptr);
int* allocateOptimizedCudaInt(size_t count);
void deallocateOptimizedCudaInt(int* ptr);
float* allocateOptimizedHostFloat(size_t count);
void deallocateOptimizedHostFloat(float* ptr);
int* allocateOptimizedHostInt(size_t count);
void deallocateOptimizedHostInt(int* ptr);

} // namespace scr5000
