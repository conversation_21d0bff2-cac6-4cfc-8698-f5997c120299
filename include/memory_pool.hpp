#pragma once

#include <memory>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <atomic>
#include <queue>
#include <cuda_runtime.h>
#include <spdlog/spdlog.h>

namespace scr5000 {

// ==================== 内存块结构 ====================
struct MemoryBlock {
    void* ptr;
    size_t size;
    bool is_free;
    std::chrono::steady_clock::time_point last_used;
    
    MemoryBlock(void* p, size_t s) 
        : ptr(p), size(s), is_free(true), last_used(std::chrono::steady_clock::now()) {}
};

// ==================== CUDA内存池 ====================
class CudaMemoryPool {
private:
    std::vector<std::unique_ptr<MemoryBlock>> blocks_;
    std::unordered_map<void*, size_t> ptr_to_index_;
    mutable std::mutex mutex_;
    
    size_t total_allocated_;
    size_t total_used_;
    size_t max_pool_size_;
    
    // 配置参数
    static constexpr size_t DEFAULT_BLOCK_SIZE = 1024 * 1024; // 1MB
    static constexpr size_t MAX_POOL_SIZE = 512 * 1024 * 1024; // 512MB
    static constexpr size_t ALIGNMENT = 256; // CUDA内存对齐

public:
    explicit CudaMemoryPool(size_t max_size = MAX_POOL_SIZE) 
        : total_allocated_(0), total_used_(0), max_pool_size_(max_size) {
        spdlog::info("CUDA memory pool created with max size: {}MB", max_size / (1024*1024));
    }
    
    ~CudaMemoryPool() {
        cleanup();
    }
    
    // 禁止拷贝和移动
    CudaMemoryPool(const CudaMemoryPool&) = delete;
    CudaMemoryPool& operator=(const CudaMemoryPool&) = delete;
    CudaMemoryPool(CudaMemoryPool&&) = delete;
    CudaMemoryPool& operator=(CudaMemoryPool&&) = delete;
    
    void* allocate(size_t size) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        // 对齐大小
        size = alignSize(size);
        
        // 首先尝试找到合适的空闲块
        for (auto& block : blocks_) {
            if (block->is_free && block->size >= size) {
                block->is_free = false;
                block->last_used = std::chrono::steady_clock::now();
                total_used_ += block->size;
                
                spdlog::debug("CUDA pool: reused block {}B at {}", block->size, block->ptr);
                return block->ptr;
            }
        }
        
        // 如果没有合适的块，分配新块
        size_t block_size = std::max(size, DEFAULT_BLOCK_SIZE);
        
        // 检查池大小限制
        if (total_allocated_ + block_size > max_pool_size_) {
            // 尝试清理未使用的块
            if (!cleanup_unused_blocks()) {
                spdlog::error("CUDA pool: out of memory (limit: {}MB)", max_pool_size_ / (1024*1024));
                return nullptr;
            }
            
            // 再次检查
            if (total_allocated_ + block_size > max_pool_size_) {
                spdlog::error("CUDA pool: still out of memory after cleanup");
                return nullptr;
            }
        }
        
        void* ptr = nullptr;
        cudaError_t status = cudaMalloc(&ptr, block_size);
        if (status != cudaSuccess) {
            spdlog::error("CUDA malloc failed: {} (size: {}B)", cudaGetErrorString(status), block_size);
            return nullptr;
        }
        
        auto block = std::make_unique<MemoryBlock>(ptr, block_size);
        block->is_free = false;
        block->last_used = std::chrono::steady_clock::now();
        
        ptr_to_index_[ptr] = blocks_.size();
        blocks_.push_back(std::move(block));
        
        total_allocated_ += block_size;
        total_used_ += block_size;
        
        spdlog::debug("CUDA pool: allocated new block {}B at {} (total: {}MB)", 
                     block_size, ptr, total_allocated_ / (1024*1024));
        
        return ptr;
    }
    
    void deallocate(void* ptr) {
        if (!ptr) return;
        
        std::lock_guard<std::mutex> lock(mutex_);
        
        auto it = ptr_to_index_.find(ptr);
        if (it == ptr_to_index_.end()) {
            spdlog::warn("CUDA pool: attempt to deallocate unknown pointer {}", ptr);
            return;
        }
        
        size_t index = it->second;
        if (index >= blocks_.size()) {
            spdlog::error("CUDA pool: invalid block index {}", index);
            return;
        }
        
        auto& block = blocks_[index];
        if (block->is_free) {
            spdlog::warn("CUDA pool: double free detected for pointer {}", ptr);
            return;
        }
        
        block->is_free = true;
        block->last_used = std::chrono::steady_clock::now();
        total_used_ -= block->size;
        
        spdlog::debug("CUDA pool: freed block {}B at {}", block->size, ptr);
    }
    
    void cleanup() {
        std::lock_guard<std::mutex> lock(mutex_);

        spdlog::info("CUDA pool cleanup: freeing {} blocks ({}MB)",
                    blocks_.size(), total_allocated_ / (1024*1024));

        for (auto& block : blocks_) {
            if (block->ptr) {
                cudaError_t status = cudaFree(block->ptr);
                if (status != cudaSuccess) {
                    spdlog::warn("CUDA free failed during cleanup: {}", cudaGetErrorString(status));
                }
            }
        }

        blocks_.clear();
        ptr_to_index_.clear();
        total_allocated_ = 0;
        total_used_ = 0;
    }
    
    // 统计信息
    struct PoolStats {
        size_t total_allocated;
        size_t total_used;
        size_t num_blocks;
        size_t num_free_blocks;
        double utilization_ratio;
    };
    
    PoolStats getStats() const {
        std::lock_guard<std::mutex> lock(mutex_);
        
        size_t free_blocks = 0;
        for (const auto& block : blocks_) {
            if (block->is_free) {
                free_blocks++;
            }
        }
        
        return {
            total_allocated_,
            total_used_,
            blocks_.size(),
            free_blocks,
            total_allocated_ > 0 ? static_cast<double>(total_used_) / total_allocated_ : 0.0
        };
    }
    
    std::string getStatsString() const {
        auto stats = getStats();
        return fmt::format(
            "CUDA Pool Stats: {:.1f}MB allocated, {:.1f}MB used ({:.1f}%), {} blocks ({} free)",
            stats.total_allocated / (1024.0*1024.0),
            stats.total_used / (1024.0*1024.0),
            stats.utilization_ratio * 100.0,
            stats.num_blocks,
            stats.num_free_blocks
        );
    }

private:
    size_t alignSize(size_t size) const {
        return (size + ALIGNMENT - 1) & ~(ALIGNMENT - 1);
    }
    
    bool cleanup_unused_blocks() {
        auto now = std::chrono::steady_clock::now();
        auto threshold = std::chrono::minutes(5); // 5分钟未使用的块可以被清理
        
        size_t freed_memory = 0;
        auto it = blocks_.begin();
        
        while (it != blocks_.end()) {
            auto& block = *it;
            if (block->is_free && (now - block->last_used) > threshold) {
                // 清理这个块
                ptr_to_index_.erase(block->ptr);
                
                cudaError_t status = cudaFree(block->ptr);
                if (status != cudaSuccess) {
                    spdlog::warn("CUDA free failed during cleanup: {}", cudaGetErrorString(status));
                }
                
                freed_memory += block->size;
                total_allocated_ -= block->size;
                
                // 更新索引映射
                size_t removed_index = std::distance(blocks_.begin(), it);
                for (auto& pair : ptr_to_index_) {
                    if (pair.second > removed_index) {
                        pair.second--;
                    }
                }
                
                it = blocks_.erase(it);
            } else {
                ++it;
            }
        }
        
        if (freed_memory > 0) {
            spdlog::info("CUDA pool: cleaned up {}MB of unused memory", freed_memory / (1024*1024));
        }
        
        return freed_memory > 0;
    }
};

// ==================== 主机内存池 ====================
class HostMemoryPool {
private:
    std::vector<std::unique_ptr<MemoryBlock>> blocks_;
    std::unordered_map<void*, size_t> ptr_to_index_;
    mutable std::mutex mutex_;
    
    size_t total_allocated_;
    size_t total_used_;
    size_t max_pool_size_;
    
    static constexpr size_t DEFAULT_BLOCK_SIZE = 1024 * 1024; // 1MB
    static constexpr size_t MAX_POOL_SIZE = 1024 * 1024 * 1024; // 1GB
    static constexpr size_t ALIGNMENT = 64; // 缓存行对齐

public:
    explicit HostMemoryPool(size_t max_size = MAX_POOL_SIZE) 
        : total_allocated_(0), total_used_(0), max_pool_size_(max_size) {
        spdlog::info("Host memory pool created with max size: {}MB", max_size / (1024*1024));
    }
    
    ~HostMemoryPool() {
        cleanup();
    }
    
    // 禁止拷贝和移动
    HostMemoryPool(const HostMemoryPool&) = delete;
    HostMemoryPool& operator=(const HostMemoryPool&) = delete;
    HostMemoryPool(HostMemoryPool&&) = delete;
    HostMemoryPool& operator=(HostMemoryPool&&) = delete;
    
    void* allocate(size_t size);
    void deallocate(void* ptr);
    void cleanup();
    
    std::string getStatsString() const;

private:
    size_t alignSize(size_t size) const {
        return (size + ALIGNMENT - 1) & ~(ALIGNMENT - 1);
    }
};

// ==================== 内存池管理器 ====================
class MemoryPoolManager {
private:
    std::unique_ptr<CudaMemoryPool> cuda_pool_;
    std::unique_ptr<HostMemoryPool> host_pool_;
    std::atomic<bool> initialized_;
    mutable std::mutex mutex_;

public:
    MemoryPoolManager();
    ~MemoryPoolManager();

    bool initialize(size_t cuda_pool_size = 512 * 1024 * 1024,  // 512MB
                   size_t host_pool_size = 1024 * 1024 * 1024); // 1GB
    void cleanup();
    bool isInitialized() const;

    void* allocateCuda(size_t size);
    void* allocateHost(size_t size);
    void deallocateCuda(void* ptr);
    void deallocateHost(void* ptr);

    std::string getStatsString() const;
};

// 注意：内存池管理器现在通过UnifiedResourceManager统一管理

} // namespace scr5000
